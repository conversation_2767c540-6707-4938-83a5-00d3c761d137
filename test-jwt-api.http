### JWT Authentication API Tests

@baseUrl = https://localhost:7126
@contentType = application/json

### 1. Register a new user
POST {{baseUrl}}/api/auth/register
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "test123",
  "confirmPassword": "test123",
  "name": "Test User"
}

### 2. <PERSON>gin with default admin account
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "admin123",
  "rememberMe": false
}

### 3. Login with test user
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "test123",
  "rememberMe": false
}

### 4. Test JWT Authentication (Replace with actual token)
GET {{baseUrl}}/api/user/test
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 5. Get User Profile (Replace with actual token)
GET {{baseUrl}}/api/user/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 6. Admin Test (Replace with admin token)
GET {{baseUrl}}/api/user/admin-test
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 7. Refresh JWT Token (Replace with actual refresh token)
POST {{baseUrl}}/api/auth/refresh-jwt-token
Content-Type: {{contentType}}

{
  "refreshToken": "base64-encoded-refresh-token"
}

### 8. Logout (Replace with actual token)
POST {{baseUrl}}/api/auth/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

### 9. Test invalid token
GET {{baseUrl}}/api/user/test
Authorization: Bearer invalid-token

### 10. Test without token
GET {{baseUrl}}/api/user/test
