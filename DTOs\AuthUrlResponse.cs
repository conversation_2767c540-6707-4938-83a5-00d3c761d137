﻿namespace LSB.SellerMailTracker.API.DTOs
{
    public class AuthUrlResponse
    {
        public bool Success { get; set; }
        public string? AuthUrl { get; set; }
        public string? AccountId { get; set; }
        public string? Message { get; set; }
        public object? SellerInfo { get; set; }
        public string? Status { get; set; }
        public string[]? Instructions { get; set; }
        public string? Error { get; set; }
    }
}
