{"Version": 1, "Hash": "k6R/TPZXN6e5qUhSr5q6gvqR+HbT39FN+MaWGOLssVA=", "Source": "LSB.SellerMailTracker.API", "BasePath": "_content/LSB.SellerMailTracker.API", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "LSB.SellerMailTracker.API\\wwwroot", "Source": "LSB.SellerMailTracker.API", "ContentRoot": "D:\\CodeBase\\LSB.SellerMailTracker.API\\LSB.SellerMailTracker.API\\wwwroot\\", "BasePath": "_content/LSB.SellerMailTracker.API", "Pattern": "**"}], "Assets": [], "Endpoints": []}