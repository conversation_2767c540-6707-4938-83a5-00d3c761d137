﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace LSB.SellerMailTracker.API.Models
{
    public class ProcessedEmail
    {
        /// <summary>
        /// Unique identifier for the processed email
        /// </summary>
        public string Id { get; set; } = Guid.NewGuid().ToString();

        /// <summary>
        /// Gmail message ID from Google API
        /// </summary>
        [Required]
        public string GmailId { get; set; } = string.Empty;

        /// <summary>
        /// Email address of the Gmail account that received this email
        /// </summary>
        [Required]
        [EmailAddress]
        public string AccountEmail { get; set; } = string.Empty;

        /// <summary>
        /// Email subject line
        /// </summary>
        public string Subject { get; set; } = string.Empty;

        /// <summary>
        /// Full email body content
        /// </summary>
        public string Body { get; set; } = string.Empty;

        /// <summary>
        /// Sender's email address
        /// </summary>
        [EmailAddress]
        public string FromEmail { get; set; } = string.Empty;

        /// <summary>
        /// Extracted monetary amount from the email
        /// </summary>
        [Range(0, double.MaxValue, ErrorMessage = "Amount must be non-negative")]
        public decimal Amount { get; set; }

        /// <summary>
        /// Currency code (USD, VND, EUR, etc.)
        /// </summary>
        [StringLength(3, MinimumLength = 3, ErrorMessage = "Currency must be 3 characters")]
        public string Currency { get; set; } = "USD";

        /// <summary>
        /// Original email date/time
        /// </summary>
        public DateTime EmailDate { get; set; }

        /// <summary>
        /// When this email was processed by the system
        /// </summary>
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;

        /// <summary>
        /// ID of the filter that matched this email
        /// </summary>
        [Required]
        public string FilterId { get; set; } = string.Empty;

        /// <summary>
        /// Name of the filter that matched this email
        /// </summary>
        public string FilterName { get; set; } = string.Empty;

        /// <summary>
        /// Structured data extracted from the email using filter extraction rules
        /// Examples: sender_name, payment_id, transaction_id, fee, etc.
        /// </summary>
        public Dictionary<string, object> ExtractedData { get; set; } = new();

        /// <summary>
        /// Additional metadata about the processing
        /// Examples: filter_type, platform, processing_timestamp, original_from, etc.
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();

        /// <summary>
        /// Helper method to get extracted data value safely
        /// </summary>
        /// <param name="key">The key to look for</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>The extracted value or default</returns>
        public T GetExtractedValue<T>(string key, T defaultValue = default(T))
        {
            if (ExtractedData.ContainsKey(key) && ExtractedData[key] is T value)
            {
                return value;
            }
            return defaultValue;
        }

        /// <summary>
        /// Helper method to get metadata value safely
        /// </summary>
        /// <param name="key">The key to look for</param>
        /// <param name="defaultValue">Default value if key not found</param>
        /// <returns>The metadata value or default</returns>
        public T GetMetadataValue<T>(string key, T defaultValue = default(T))
        {
            if (Metadata.ContainsKey(key) && Metadata[key] is T value)
            {
                return value;
            }
            return defaultValue;
        }

        /// <summary>
        /// Get the platform that sent this email (from metadata)
        /// </summary>
        [JsonIgnore]
        public string Platform => GetMetadataValue<string>("platform", "unknown");

        /// <summary>
        /// Get the filter type that processed this email (from metadata)
        /// </summary>
        [JsonIgnore]
        public string FilterType => GetMetadataValue<string>("filter_type", "custom");

        /// <summary>
        /// Check if this is an income or outcome transaction
        /// </summary>
        [JsonIgnore]
        public bool IsIncome => FilterType == "income";

        /// <summary>
        /// Check if this is an outcome transaction
        /// </summary>
        [JsonIgnore]
        public bool IsOutcome => FilterType == "outcome";

        /// <summary>
        /// Get sender name from extracted data or from email
        /// </summary>
        [JsonIgnore]
        public string SenderName => GetExtractedValue<string>("sender_name", FromEmail);

        /// <summary>
        /// Get payment/transaction ID from extracted data
        /// </summary>
        [JsonIgnore]
        public string TransactionId => GetExtractedValue<string>("payment_id", 
            GetExtractedValue<string>("transaction_id", string.Empty));

        /// <summary>
        /// Get fee amount if available
        /// </summary>
        [JsonIgnore]
        public decimal Fee => GetExtractedValue<decimal>("fee", 0m);
    }
}