﻿using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class TemplateService : ITemplateService
    {
        private readonly List<EmailTemplate> _emailTemplates;

        public TemplateService()
        {
            _emailTemplates = InitializeEmailTemplates();
        }

        public List<EmailTemplate> GetEmailTemplates()
        {
            return _emailTemplates;
        }

        public EmailTemplate? GetEmailTemplate(string templateId)
        {
            return _emailTemplates.FirstOrDefault(t => t.Id == templateId);
        }

        public async Task<EmailFilter> CreateSmartIncomeFilter()
        {
            var incomeTemplates = _emailTemplates.Where(t => t.Type == "income").ToList();
            return await CreateSmartFilter("income", incomeTemplates);
        }

        public async Task<EmailFilter> CreateSmartOutcomeFilter()
        {
            var outcomeTemplates = _emailTemplates.Where(t => t.Type == "outcome").ToList();
            return await CreateSmartFilter("outcome", outcomeTemplates);
        }

        public async Task ApplyTemplateToFilter(EmailFilter filter, string templateId)
        {
            var template = GetEmailTemplate(templateId);
            if (template == null) return;

            filter.Conditions = template.Conditions.Select(c => new FilterCondition
            {
                Id = Guid.NewGuid().ToString(),
                Type = c.Type,
                Operator = c.Operator,
                Value = c.Value
            }).ToList();

            filter.ExtractionRules = new Dictionary<string, ExtractionRule>(template.ExtractionRules);
        }

        public async Task ApplySmartRulesToFilter(EmailFilter filter, string type)
        {
            var relevantTemplates = _emailTemplates.Where(t => t.Type == type).ToList();

            var allConditions = new List<FilterCondition>();
            var allExtractionRules = new Dictionary<string, ExtractionRule>();

            foreach (var template in relevantTemplates)
            {
                foreach (var condition in template.Conditions)
                {
                    allConditions.Add(new FilterCondition
                    {
                        Id = Guid.NewGuid().ToString(),
                        Type = condition.Type,
                        Operator = condition.Operator,
                        Value = condition.Value
                    });
                }

                foreach (var rule in template.ExtractionRules)
                {
                    if (!allExtractionRules.ContainsKey(rule.Key))
                    {
                        allExtractionRules[rule.Key] = new ExtractionRule
                        {
                            Patterns = new List<string>()
                        };
                    }
                    allExtractionRules[rule.Key].Patterns.AddRange(rule.Value.Patterns);
                }
            }

            filter.Conditions = allConditions;
            filter.ExtractionRules = allExtractionRules;
            filter.MatchCondition = "any";
        }

        public List<string> GetSupportedPlatforms()
        {
            return _emailTemplates.Select(t => t.Platform).Distinct().ToList();
        }

        private async Task<EmailFilter> CreateSmartFilter(string type, List<EmailTemplate> templates)
        {
            var filter = new EmailFilter
            {
                Id = Guid.NewGuid().ToString(),
                Name = $"Smart {(type == "income" ? "Thu nhập" : "Chi tiêu")} Detection",
                Type = type,
                Description = $"Tự động phát hiện email {(type == "income" ? "thu nhập" : "chi tiêu")} từ tất cả platforms",
                IsActive = true,
                MatchCondition = "any",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await ApplySmartRulesToFilter(filter, type);
            return filter;
        }

        private List<EmailTemplate> InitializeEmailTemplates()
        {
            return new List<EmailTemplate>
            {
                // INCOME TEMPLATES
                new EmailTemplate
                {
                    Id = "income_payoneer_receive_en",
                    Type = "income",
                    Name = "Payoneer - Payment Received (English)",
                    Language = "en",
                    Platform = "Payoneer",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "payoneer.com" },
                        // Chỉ cần 1 trong 2 conditions này match
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "received a payment" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "received a payment" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                // Safe patterns - giới hạn 1-8 chữ số trước dấu thập phân
                                @"Amount[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"\$([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*(?:USD|$)",
                                @"([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*USD",
                                @"Số tiền[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        },
                        ["sender_name"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Sent by[:\s]*([^\n\r]{1,100})",
                                @"Được gửi bởi[:\s]*([^\n\r]{1,100})",
                                @"from\s+([^\n\r]{1,100})"
                            }
                        }
                    }
                },
                new EmailTemplate
                {
                    Id = "income_payoneer_receive_vi",
                    Type = "income",
                    Name = "Payoneer - Nhận Thanh Toán (Tiếng Việt)",
                    Language = "vi",
                    Platform = "Payoneer",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "payoneer.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "nhận được một khoản thanh toán" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "nhận được một khoản thanh toán" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Số tiền[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"Amount[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"\$([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*(?:USD|$)",
                                @"([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*USD"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        }
                    }
                },
                // ENHANCED LIANLIAN INCOME TEMPLATES
                new EmailTemplate
                {
                    Id = "income_lianlian_receive_vi",
                    Type = "income",
                    Name = "LianLian Global - Nhận Thanh Toán (Tiếng Việt)",
                    Language = "vi",
                    Platform = "LianLian Global",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "lianlianpay.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "Nhận thanh toán thành công" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "Nhận thanh toán thành công" },
                        new() { Id = "4", Type = "body", Operator = "contains", Value = "Payment received successfully" },
                        new() { Id = "5", Type = "body", Operator = "contains", Value = "You have received funds" },
                        new() { Id = "6", Type = "subject", Operator = "contains", Value = "You have received funds" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                // Super simple patterns để bắt mọi số có USD
                                @"(\d+(?:,\d{3})*(?:\.\d{2})?)\s*USD",
                                @"USD\s*(\d+(?:,\d{3})*(?:\.\d{2})?)",
                                @"\$\s*(\d+(?:,\d{3})*(?:\.\d{2})?)",
                                @"(\d+\.\d{2})\s*USD",
                                @"(\d+,\d{3}\.\d{2})\s*USD",
                                @"(\d{1,8})\s*USD",
                                @"(\d{1,8}\.\d{1,2})\s*USD",
                                // Rất đơn giản - chỉ cần số + USD
                                @"(\d+)\s*USD",
                                @"USD\s*(\d+)",
                                // Bắt mọi pattern có số và USD gần nhau
                                @"(\d+(?:\.\d{2})?)\s*USD",
                                @"USD\s*(\d+(?:\.\d{2})?)"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        }
                    }
                },
                new EmailTemplate
                {
                    Id = "income_lianlian_receive_en",
                    Type = "income",
                    Name = "LianLian Global - Payment Received (English)",
                    Language = "en",
                    Platform = "LianLian Global",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "lianlianpay.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "Payment received successfully" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "received funds" },
                        new() { Id = "4", Type = "subject", Operator = "contains", Value = "You have received funds" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                // Super simple patterns
                                @"(\d+(?:,\d{3})*(?:\.\d{2})?)\s*USD",
                                @"USD\s*(\d+(?:,\d{3})*(?:\.\d{2})?)",
                                @"(\d+\.\d{2})\s*USD",
                                @"(\d+,\d{3}\.\d{2})\s*USD",
                                @"(\d{1,8})\s*USD",
                                @"(\d{1,8}\.\d{1,2})\s*USD",
                                @"(\d+)\s*USD",
                                @"USD\s*(\d+)",
                                @"(\d+(?:\.\d{2})?)\s*USD",
                                @"USD\s*(\d+(?:\.\d{2})?)"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        }
                    }
                },
                new EmailTemplate
                {
                    Id = "income_paypal_receive",
                    Type = "income",
                    Name = "PayPal - Payment Received",
                    Language = "en",
                    Platform = "PayPal",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "paypal.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "received a payment" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "sent you" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"\$([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*(?:USD|$)",
                                @"Amount[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)"
                            }
                        },
                        ["sender_name"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"([^\n\r]{1,100})\s+sent you",
                                @"Payment from\s+([^\n\r]{1,100})"
                            }
                        }
                    }
                },

                // OUTCOME TEMPLATES  
                new EmailTemplate
                {
                    Id = "outcome_payoneer_send_en",
                    Type = "outcome",
                    Name = "Payoneer - Payment Sent (English)",
                    Language = "en",
                    Platform = "Payoneer",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "payoneer.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "successfully credited" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "successfully credited" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Amount[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"Total amount charged[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"\$([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*USD"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        },
                        ["fee"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Payment fee[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"Phí thanh toán[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)"
                            }
                        }
                    }
                },
                new EmailTemplate
                {
                    Id = "outcome_payoneer_send_vi",
                    Type = "outcome",
                    Name = "Payoneer - Gửi Thanh Toán (Tiếng Việt)",
                    Language = "vi",
                    Platform = "Payoneer",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "payoneer.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "ghi có thành công" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "ghi có thành công" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Số tiền[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"Tổng số tiền[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"\$([1-9][\d,]{0,7}(?:\.\d{1,2})?)\s*USD"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        },
                        ["fee"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Phí thanh toán[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)",
                                @"Payment fee[:\s]*\$?([1-9][\d,]{0,7}(?:\.\d{1,2})?)"
                            }
                        }
                    }
                },
                // ENHANCED LIANLIAN OUTCOME TEMPLATES
                new EmailTemplate
                {
                    Id = "outcome_lianlian_send_vi",
                    Type = "outcome",
                    Name = "LianLian Global - Gửi Thanh Toán (Tiếng Việt)",
                    Language = "vi",
                    Platform = "LianLian Global",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "lianlianpay.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "Gửi thanh toán thành công" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "Gửi thanh toán thành công" },
                        new() { Id = "4", Type = "body", Operator = "contains", Value = "Payment completed" },
                        new() { Id = "5", Type = "subject", Operator = "contains", Value = "Payment completed" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                // General patterns for LianLian send
                                @"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD", // General USD pattern
                                @"USD\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)", // USD prefix
                                @"\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)", // Dollar sign
                                // Vietnamese patterns
                                @"Số tiền giao dịch[:\s]*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD",
                                @"số tiền\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD",
                                @"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD\s*đã",
                                // English patterns  
                                @"Transfer Amount[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                @"Amount[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                // Table patterns
                                @"Transfer Amount\s*Số tiền giao dịch[^0-9]*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                @"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD[^A-Za-z]*(?:Subtotal|Tổng Số Tiền)",
                                // More flexible patterns
                                @"(\d{1,8}(?:\.\d{1,2})?)\s*USD(?!\s*Tỷ)",
                                @"(\d{1,8}(?:\.\d{1,2})?)\s*USD\s*(?:đã|sent|transfer)"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        },
                        ["fee"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Fee Amount[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                @"Phí Giao Dịch[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                @"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD[^A-Za-z]*(?:Fee|Phí)"
                            }
                        }
                    }
                },
                new EmailTemplate
                {
                    Id = "outcome_lianlian_send_en",
                    Type = "outcome",
                    Name = "LianLian Global - Payment Sent (English)",
                    Language = "en",
                    Platform = "LianLian Global",
                    Conditions = new List<FilterCondition>
                    {
                        new() { Id = "1", Type = "sender", Operator = "contains", Value = "lianlianpay.com" },
                        new() { Id = "2", Type = "subject", Operator = "contains", Value = "Payment completed" },
                        new() { Id = "3", Type = "body", Operator = "contains", Value = "payment has been successfully sent" }
                    },
                    ExtractionRules = new Dictionary<string, ExtractionRule>
                    {
                        ["amount"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*USD", // General USD pattern
                                @"USD\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)", // USD prefix
                                @"Transfer Amount[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                @"Amount[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)"
                            }
                        },
                        ["currency"] = new ExtractionRule
                        {
                            Patterns = new List<string> { @"\b(USD|VND|EUR|GBP|HKD)\b" }
                        },
                        ["fee"] = new ExtractionRule
                        {
                            Patterns = new List<string>
                            {
                                @"Fee Amount[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)",
                                @"Transaction Fee[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)"
                            }
                        }
                    }
                }
            };
        }
    }
}