﻿using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.DTOs;

namespace LSB.SellerMailTracker.API.Extensions
{
    public static class MappingExtensions
    {
        // Updated Extension Method
        public static GmailAccountResponse ToResponse(this GmailAccount account)
        {
            return new GmailAccountResponse
            {
                Id = account.Id,
                Email = account.Email,
                DisplayName = account.DisplayName,
                CreatedAt = account.CreatedAt,
                IsActive = account.IsActive,
                LastSyncAt = account.LastSyncAt,
                TokenExpired = DateTime.UtcNow >= account.TokenExpiry,

                // ✅ ADD THIS: Map SellerInfo
                SellerInfo = account.SellerInfo
            };
        }

        public static List<GmailAccountResponse> ToResponse(this List<GmailAccount> accounts)
        {
            return accounts.Select(a => a.ToResponse()).ToList();
        }

        public static EmailFilter ToModel(this CreateFilterRequest request)
        {
            return new EmailFilter
            {
                Name = request.Name,
                BodyContains = request.BodyContains,
                Description = request.Description,
                IsActive = request.IsActive,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };
        }

        public static EmailFilterResponse ToResponse(this EmailFilter filter)
        {
            return new EmailFilterResponse
            {
                Id = filter.Id,
                Name = filter.Name,
                BodyContains = filter.BodyContains,
                Description = filter.Description,
                IsActive = filter.IsActive,
                CreatedAt = filter.CreatedAt,
                UpdatedAt = filter.UpdatedAt
            };
        }

        public static List<EmailFilterResponse> ToResponse(this List<EmailFilter> filters)
        {
            return filters.Select(f => f.ToResponse()).ToList();
        }

        public static void UpdateFromRequest(this EmailFilter filter, UpdateFilterRequest request)
        {
            filter.Name = request.Name;
            filter.BodyContains = request.BodyContains;
            filter.Description = request.Description;
            filter.IsActive = request.IsActive;
            filter.UpdatedAt = DateTime.UtcNow;
        }

        public static ProcessedEmailResponse ToResponse(this ProcessedEmail email, string? filterName = null)
        {
            return new ProcessedEmailResponse
            {
                Id = email.Id,
                GmailId = email.GmailId,
                AccountEmail = email.AccountEmail,
                Subject = email.Subject,
                FromEmail = email.FromEmail,
                Amount = email.Amount,
                Currency = email.Currency,
                EmailDate = email.EmailDate,
                ProcessedAt = email.ProcessedAt,
                FilterId = email.FilterId,
                FilterName = filterName ?? email.FilterName ?? ""
            };
        }

        public static List<ProcessedEmailResponse> ToResponse(this List<ProcessedEmail> emails, List<EmailFilter>? filters = null)
        {
            var filterDict = filters?.ToDictionary(f => f.Id, f => f.Name) ?? new Dictionary<string, string>();

            return emails.Select(e => e.ToResponse(filterDict.GetValueOrDefault(e.FilterId, e.FilterName ?? ""))).ToList();
        }

        public static List<ProcessedEmailResponse> ToResponse(this IEnumerable<ProcessedEmail> emails, List<EmailFilter>? filters = null)
        {
            var filterDict = filters?.ToDictionary(f => f.Id, f => f.Name) ?? new Dictionary<string, string>();

            return emails.Select(e => e.ToResponse(filterDict.GetValueOrDefault(e.FilterId, e.FilterName ?? ""))).ToList();
        }

        public static DashboardStatsResponse ToResponse(this DashboardStats stats,
            List<ProcessedEmail> recentEmails,
            List<EmailFilter> filters)
        {
            var today = DateTime.UtcNow.Date;
            var thisWeek = today.AddDays(-(int)today.DayOfWeek);
            var thisMonth = new DateTime(today.Year, today.Month, 1);

            var emailsToday = recentEmails.Where(e => e.EmailDate.Date == today).ToList();
            var emailsThisWeek = recentEmails.Where(e => e.EmailDate.Date >= thisWeek).ToList();
            var emailsThisMonth = recentEmails.Where(e => e.EmailDate.Date >= thisMonth).ToList();

            var currencyGroups = recentEmails
                .GroupBy(e => e.Currency)
                .Select(g => new CurrencySummary
                {
                    Currency = g.Key,
                    TotalAmount = g.Sum(e => e.Amount),
                    EmailCount = g.Count()
                })
                .OrderByDescending(c => c.EmailCount)
                .Take(5)
                .ToList();

            return new DashboardStatsResponse
            {
                TotalAccounts = stats.TotalAccounts,
                ActiveAccounts = stats.TotalAccounts,
                ActiveFilters = stats.ActiveFilters,
                TotalEmailsProcessed = stats.TotalEmailsProcessed,
                TotalAmount = stats.TotalAmount,
                LastProcessedDate = stats.LastProcessedDate != DateTime.MinValue ? stats.LastProcessedDate : null,

                EmailsToday = emailsToday.Count,
                EmailsThisWeek = emailsThisWeek.Count,
                EmailsThisMonth = emailsThisMonth.Count,
                AmountToday = emailsToday.Sum(e => e.Amount),

                TopCurrencies = currencyGroups,
                RecentEmails = recentEmails.Take(10).ToResponse(filters)
            };
        }

        public static ProcessEmailsResponse ToProcessResponse(this List<ProcessedEmail> processedEmails,
            int totalProcessed,
            DateTime startTime,
            List<EmailFilter> filters)
        {
            var endTime = DateTime.UtcNow;

            var currencySummaries = processedEmails
                .GroupBy(e => e.Currency)
                .ToDictionary(g => g.Key, g => new CurrencySummary
                {
                    Currency = g.Key,
                    TotalAmount = g.Sum(e => e.Amount),
                    EmailCount = g.Count()
                });

            return new ProcessEmailsResponse
            {
                TotalProcessed = totalProcessed,
                NewEmails = processedEmails.Count,
                SkippedEmails = totalProcessed - processedEmails.Count,
                TotalAmount = processedEmails.Sum(e => e.Amount),
                ProcessedEmails = processedEmails.ToResponse(filters),
                ProcessingStartTime = startTime,
                ProcessingEndTime = endTime,
                CurrencySummaries = currencySummaries
            };
        }

        public static PagedResponse<T> ToPagedResponse<T>(this List<T> items, int page, int pageSize, int totalItems)
        {
            return new PagedResponse<T>
            {
                Items = items,
                TotalItems = totalItems,
                TotalPages = (int)Math.Ceiling((double)totalItems / pageSize),
                CurrentPage = page,
                PageSize = pageSize
            };
        }

        public static IQueryable<T> ApplyPaging<T>(this IQueryable<T> query, PagedRequest request)
        {
            return query
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize);
        }

        public static List<T> ApplyPaging<T>(this List<T> list, PagedRequest request)
        {
            return list
                .Skip((request.Page - 1) * request.PageSize)
                .Take(request.PageSize)
                .ToList();
        }
    }
}