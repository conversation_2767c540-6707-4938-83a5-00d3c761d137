{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"LSB.SellerMailTracker.API/1.0.0": {"dependencies": {"FirebaseAdmin": "3.0.0", "Google.Cloud.Firestore": "3.7.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "9.0.0", "Microsoft.AspNetCore.OpenApi": "9.0.7", "Swashbuckle.AspNetCore": "7.2.0"}, "runtime": {"LSB.SellerMailTracker.API.dll": {}}}, "FirebaseAdmin/3.0.0": {"dependencies": {"Google.Api.Gax.Rest": "4.8.0", "Google.Apis.Auth": "1.67.0", "System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/net6.0/FirebaseAdmin.dll": {"assemblyVersion": "3.0.0.0", "fileVersion": "3.0.0.0"}}}, "Google.Api.CommonProtos/2.15.0": {"dependencies": {"Google.Protobuf": "3.25.0"}, "runtime": {"lib/netstandard2.0/Google.Api.CommonProtos.dll": {"assemblyVersion": "2.15.0.0", "fileVersion": "2.15.0.0"}}}, "Google.Api.Gax/4.8.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Api.Gax.Grpc/4.8.0": {"dependencies": {"Google.Api.CommonProtos": "2.15.0", "Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "1.67.0", "Grpc.Auth": "2.60.0", "Grpc.Core.Api": "2.60.0", "Grpc.Net.Client": "2.60.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Grpc.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Api.Gax.Rest/4.8.0": {"dependencies": {"Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "1.67.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}}, "Google.Apis/1.67.0": {"dependencies": {"Google.Apis.Core": "1.67.0"}, "runtime": {"lib/net6.0/Google.Apis.dll": {"assemblyVersion": "1.67.0.0", "fileVersion": "1.67.0.0"}}}, "Google.Apis.Auth/1.67.0": {"dependencies": {"Google.Apis": "1.67.0", "Google.Apis.Core": "1.67.0", "System.Management": "7.0.2"}, "runtime": {"lib/net6.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.67.0.0", "fileVersion": "1.67.0.0"}}}, "Google.Apis.Core/1.67.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/net6.0/Google.Apis.Core.dll": {"assemblyVersion": "1.67.0.0", "fileVersion": "1.67.0.0"}}}, "Google.Cloud.Firestore/3.7.0": {"dependencies": {"Google.Cloud.Firestore.V1": "3.7.0", "System.Collections.Immutable": "8.0.0", "System.Linq.Async": "6.0.1"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "Google.Cloud.Firestore.V1/3.7.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0", "Google.Cloud.Location": "2.2.0", "Google.LongRunning": "3.2.0"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Firestore.V1.dll": {"assemblyVersion": "3.7.0.0", "fileVersion": "3.7.0.0"}}}, "Google.Cloud.Location/2.2.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0"}, "runtime": {"lib/netstandard2.0/Google.Cloud.Location.dll": {"assemblyVersion": "2.2.0.0", "fileVersion": "2.2.0.0"}}}, "Google.LongRunning/3.2.0": {"dependencies": {"Google.Api.Gax.Grpc": "4.8.0"}, "runtime": {"lib/netstandard2.0/Google.LongRunning.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Protobuf/3.25.0": {"runtime": {"lib/net5.0/Google.Protobuf.dll": {"assemblyVersion": "3.25.0.0", "fileVersion": "3.25.0.0"}}}, "Grpc.Auth/2.60.0": {"dependencies": {"Google.Apis.Auth": "1.67.0", "Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/netstandard2.0/Grpc.Auth.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Core.Api/2.60.0": {"runtime": {"lib/netstandard2.1/Grpc.Core.Api.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Client/2.60.0": {"dependencies": {"Grpc.Net.Common": "2.60.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}, "runtime": {"lib/net8.0/Grpc.Net.Client.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Grpc.Net.Common/2.60.0": {"dependencies": {"Grpc.Core.Api": "2.60.0"}, "runtime": {"lib/net8.0/Grpc.Net.Common.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.60.0.0"}}}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52903"}}}, "Microsoft.AspNetCore.OpenApi/9.0.7": {"dependencies": {"Microsoft.OpenApi": "1.6.22"}, "runtime": {"lib/net9.0/Microsoft.AspNetCore.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.725.31702"}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"runtime": {"lib/net9.0/Microsoft.IdentityModel.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Logging/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.IdentityModel.Tokens/8.0.1": {"dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}, "runtime": {"lib/net9.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "Microsoft.OpenApi/1.6.22": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Swashbuckle.AspNetCore/7.2.0": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "6.0.5", "Swashbuckle.AspNetCore.Swagger": "7.2.0", "Swashbuckle.AspNetCore.SwaggerGen": "7.2.0", "Swashbuckle.AspNetCore.SwaggerUI": "7.2.0"}}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"dependencies": {"Microsoft.OpenApi": "1.6.22"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "7.2.0"}, "runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"runtime": {"lib/net9.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "7.2.0.956"}}}, "System.CodeDom/7.0.0": {"runtime": {"lib/net7.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Collections.Immutable/8.0.0": {}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}, "runtime": {"lib/net9.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.1.50722"}}}, "System.Linq.Async/6.0.1": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}, "runtime": {"lib/net6.0/System.Linq.Async.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.1.35981"}}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/net7.0/System.Management.dll": {"assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "7.0.0.2", "fileVersion": "7.0.723.27404"}}}}}, "libraries": {"LSB.SellerMailTracker.API/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "FirebaseAdmin/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-6aNnm+RzH3ZQEprwDfJui83edH0M3ydbqx0zOCdc1ZaVyDuXB5bYhuh6dVPCHEhb8zeL+ZNySpx0cZDWJk8/vw==", "path": "firebaseadmin/3.0.0", "hashPath": "firebaseadmin.3.0.0.nupkg.sha512"}, "Google.Api.CommonProtos/2.15.0": {"type": "package", "serviceable": true, "sha512": "sha512-k+43CTEgMbT4C2JjcIJon3GvMNFJ3kLMjPTedOkXUAt0D5cFfrlee+M4oieueAVhbXktR89yoj5O/kVmC/AKJw==", "path": "google.api.commonprotos/2.15.0", "hashPath": "google.api.commonprotos.2.15.0.nupkg.sha512"}, "Google.Api.Gax/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlV8Jq/G5CQAA3PwYAuKGjfzGOP7AvjhREnE6vgZlzxREGYchHudZWa2PWSqFJL+MBtz9YgitLpRogANN3CVvg==", "path": "google.api.gax/4.8.0", "hashPath": "google.api.gax.4.8.0.nupkg.sha512"}, "Google.Api.Gax.Grpc/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-njc<PERSON>rFMZTmG2xdar6o5zyj7YVCUv6JNWU7un+q15Fm9DpGt0HSqHVnabVD2LBjUa3JWrqWX2kVv0w7qx4+tFQ==", "path": "google.api.gax.grpc/4.8.0", "hashPath": "google.api.gax.grpc.4.8.0.nupkg.sha512"}, "Google.Api.Gax.Rest/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaA5LZ2VvGj/wwIzRB68swr7khi2kWNgqWvsB0fYtScIAl3kGkGtqiBcx63H1YLeKr5xau1866bFjTeReH6FSQ==", "path": "google.api.gax.rest/4.8.0", "hashPath": "google.api.gax.rest.4.8.0.nupkg.sha512"}, "Google.Apis/1.67.0": {"type": "package", "serviceable": true, "sha512": "sha512-XM8/fViJaB1pN61OdXy5RMZoQEqd3hKlWvA/K431gFSb5XtQ48BynfgrbBkUtFcPbSRa4BdjBHzSbkBh/skyMg==", "path": "google.apis/1.67.0", "hashPath": "google.apis.1.67.0.nupkg.sha512"}, "Google.Apis.Auth/1.67.0": {"type": "package", "serviceable": true, "sha512": "sha512-Bs9BlbZ12Y4NXzMONjpzQhZr9VbwLUTGMHkcQRF36aYnk2fYrmj5HNVNh7PPHDDq1fcEQpCtPic2nSlpYQLKXw==", "path": "google.apis.auth/1.67.0", "hashPath": "google.apis.auth.1.67.0.nupkg.sha512"}, "Google.Apis.Core/1.67.0": {"type": "package", "serviceable": true, "sha512": "sha512-IPq0I3B01NYZraPoMl8muELFLg4Vr2sbfyZp4PR2Xe3MAhHkZCiKyV28Yh1L14zIKUb0X0snol1sR5/mx4S6Iw==", "path": "google.apis.core/1.67.0", "hashPath": "google.apis.core.1.67.0.nupkg.sha512"}, "Google.Cloud.Firestore/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-32gFGV7nDsjAm0dRZXFEAkZFmIJDhdQK76KUvby6AhnaokiK001uWRAIIDeghALrr0wxQNhcCNxPg2Gk9iU6LQ==", "path": "google.cloud.firestore/3.7.0", "hashPath": "google.cloud.firestore.3.7.0.nupkg.sha512"}, "Google.Cloud.Firestore.V1/3.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-BOF6p/rEkp5g+mW2ZsVOsd8m3Zarqfob2XgxbRQg33NxKI2pZ8xbpSY743rQ05t174UmdTXM9dAS8i/OhM3Ajw==", "path": "google.cloud.firestore.v1/3.7.0", "hashPath": "google.cloud.firestore.v1.3.7.0.nupkg.sha512"}, "Google.Cloud.Location/2.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-p1iW6v+BGnx9nMTOTInvphpE3OJ1h4FjwnTc4DZIhilbjnB2mkfaP4dpgbcMGqAwJZPl+cTEu/dNJhVwR39ICg==", "path": "google.cloud.location/2.2.0", "hashPath": "google.cloud.location.2.2.0.nupkg.sha512"}, "Google.LongRunning/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-aL3xbATOZCD5i8iNR1pYw/w3jEb8ndRUa6HAKCnNsEtu0FzpdqG4BvezWvJUM4kUIMjSGGBChOmnCLkjioBGrw==", "path": "google.longrunning/3.2.0", "hashPath": "google.longrunning.3.2.0.nupkg.sha512"}, "Google.Protobuf/3.25.0": {"type": "package", "serviceable": true, "sha512": "sha512-pIEkH1IqZV1iK8J5MYdG1kOyY0EoQLB6yEKvBq12RYNtvGXwCvnQg5zQsFmcqAEPtIZvSqPozIbUZaEd5a2gCg==", "path": "google.protobuf/3.25.0", "hashPath": "google.protobuf.3.25.0.nupkg.sha512"}, "Grpc.Auth/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-mJAllsSsFix/wrp1l5QmHXRlzMFmnIGZJYuHG1thkDJPUE9ltHPkdL5x30xGpqKvka02Il2oHd66gt8YfSOIWg==", "path": "grpc.auth/2.60.0", "hashPath": "grpc.auth.2.60.0.nupkg.sha512"}, "Grpc.Core.Api/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-VWah+8dGJhhsay5BQ/Ljq6GYDWj0lSjdzqyoBgUQhXTbBqhs+q5dRFROKxI1xxzlL4pfUO45cf/y+KnHVFG9ew==", "path": "grpc.core.api/2.60.0", "hashPath": "grpc.core.api.2.60.0.nupkg.sha512"}, "Grpc.Net.Client/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-J9U96gjZHOcqSgAThg9vZZhLsbTD005bUggPtMP/RVQnGc3+tQJTpkRUCJtJWq9cykNydsRVoyU38TjPP/VJ4A==", "path": "grpc.net.client/2.60.0", "hashPath": "grpc.net.client.2.60.0.nupkg.sha512"}, "Grpc.Net.Common/2.60.0": {"type": "package", "serviceable": true, "sha512": "sha512-Y/917aplgD1RA0q1cd9WpnMGyl9Luu3WZl6ZMpPvNQwg2TNw/3uXUDSriDBybeCtxnKUCtxUcWO3WsVkhM1DcA==", "path": "grpc.net.common/2.60.0", "hashPath": "grpc.net.common.2.60.0.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bs+1Pq3vQdS2lTyxNUd9fEhtMsq3eLUpK36k2t56iDMVrk6OrAoFtvrQrTK0Y0OetTcJrUkGU7hBlf+ORzHLqQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/9.0.0", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.9.0.0.nupkg.sha512"}, "Microsoft.AspNetCore.OpenApi/9.0.7": {"type": "package", "serviceable": true, "sha512": "sha512-8aG0mkgmA38IDJ0ca5HIpdexKjHXIh0z1kIdw5WyM6CrD4+CEt97UgSwBBBCHG6QQKV0hj2mfkwtEcqrJBcu8g==", "path": "microsoft.aspnetcore.openapi/9.0.7", "hashPath": "microsoft.aspnetcore.openapi.9.0.7.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/6.0.5": {"type": "package", "serviceable": true, "sha512": "sha512-Ckb5EDBUNJdFWyajfXzUIMRkhf52fHZOQuuZg/oiu8y7zDCVwD0iHhew6MnThjHmevanpxL3f5ci2TtHQEN6bw==", "path": "microsoft.extensions.apidescription.server/6.0.5", "hashPath": "microsoft.extensions.apidescription.server.6.0.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/HggWBbTwy8TgebGSX5DBZ24ndhzi93sHUBDvP1IxbZD7FDokYzdAr6+vbWGjw2XAfR2EJ1sfKUotpjHnFWPxA==", "path": "microsoft.extensions.logging.abstractions/6.0.0", "hashPath": "microsoft.extensions.logging.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.IdentityModel.Abstractions/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw==", "path": "microsoft.identitymodel.abstractions/8.0.1", "hashPath": "microsoft.identitymodel.abstractions.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "path": "microsoft.identitymodel.jsonwebtokens/8.0.1", "hashPath": "microsoft.identitymodel.jsonwebtokens.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "path": "microsoft.identitymodel.logging/8.0.1", "hashPath": "microsoft.identitymodel.logging.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "path": "microsoft.identitymodel.protocols/8.0.1", "hashPath": "microsoft.identitymodel.protocols.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "path": "microsoft.identitymodel.protocols.openidconnect/8.0.1", "hashPath": "microsoft.identitymodel.protocols.openidconnect.8.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "path": "microsoft.identitymodel.tokens/8.0.1", "hashPath": "microsoft.identitymodel.tokens.8.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.6.22": {"type": "package", "serviceable": true, "sha512": "sha512-aBvunmrdu/x+4CaA/UP1Jx4xWGwk4kymhoIRnn2Vp+zi5/KOPQJ9EkSXHRUr01WcGKtYl3Au7XfkPJbU1G2sjQ==", "path": "microsoft.openapi/1.6.22", "hashPath": "microsoft.openapi.1.6.22.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Swashbuckle.AspNetCore/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-vJv19UpWm6OOgnS9QLDnWARNVasXUfj8SFvlG7UVALm4nBnfwRnEky7C0veSDqMUmBeMPC6Ec3d6G1ts/J04Uw==", "path": "swashbuckle.aspnetcore/7.2.0", "hashPath": "swashbuckle.aspnetcore.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-y27fNDfIh1vGhJjXYynLcZjl7DLOW1bSO2MDsY9wB4Zm1fdxpPsuBSiR4U+0acWlAqLmnuOPKr/OeOgwRUkBlw==", "path": "swashbuckle.aspnetcore.swagger/7.2.0", "hashPath": "swashbuckle.aspnetcore.swagger.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-pMrTxGVuXM7t4wqft5CNNU8A0++Yw5kTLmYhB6tbEcyBfO8xEF/Y8pkJhO6BZ/2MYONrRYoQTfPFJqu8fOf5WQ==", "path": "swashbuckle.aspnetcore.swaggergen/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggergen.7.2.0.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/7.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-hgrXeKzyp5OGN8qVvL7A+vhmU7mDJTfGpiMBRL66IcfLOyna8UTLtn3cC3CghamXpRDufcc9ciklTszUGEQK0w==", "path": "swashbuckle.aspnetcore.swaggerui/7.2.0", "hashPath": "swashbuckle.aspnetcore.swaggerui.7.2.0.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/8.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "path": "system.identitymodel.tokens.jwt/8.0.1", "hashPath": "system.identitymodel.tokens.jwt.8.0.1.nupkg.sha512"}, "System.Linq.Async/6.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "path": "system.linq.async/6.0.1", "hashPath": "system.linq.async.6.0.1.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}}}