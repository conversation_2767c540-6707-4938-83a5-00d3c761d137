namespace LSB.SellerMailTracker.API.Models
{
    public class AutoScanResult
    {
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int AccountsProcessed { get; set; }
        public int EmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    public class AutoScanStatistics
    {
        public int TotalRuns { get; set; }
        public int SuccessfulRuns { get; set; }
        public int FailedRuns { get; set; }
        public int TotalEmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public double AverageEmailsPerRun { get; set; }
        public decimal AverageAmountPerRun { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public bool IsEnabled { get; set; }
        public int CurrentInterval { get; set; }
        public int ConfiguredAccounts { get; set; }
        public int ConfiguredFilters { get; set; }
        public double SuccessRate => TotalRuns > 0 ? (double)SuccessfulRuns / TotalRuns * 100 : 0;
    }

    public class AutoScanStatus
    {
        public bool IsEnabled { get; set; }
        public DateTime? LastRunTime { get; set; }
        public DateTime? NextRunTime { get; set; }
        public int IntervalMinutes { get; set; }
        public bool IsRunning { get; set; }
        public string LastRunResult { get; set; } = string.Empty;
        public int TotalEmailsProcessed { get; set; }
    }
}
