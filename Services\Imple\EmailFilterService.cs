﻿using System.Text.RegularExpressions;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class EmailFilterService : IEmailFilterService
    {
        private readonly IDataService _dataService;

        public EmailFilterService(IDataService dataService)
        {
            _dataService = dataService;
        }

        public async Task<List<ProcessedEmail>> ProcessEmailsAsync(List<GmailMessage> messages, List<EmailFilter> filters, string accountEmail)
        {
            var processedEmails = new List<ProcessedEmail>();

            foreach (var message in messages)
            {
                var subject = GetHeaderValue(message.Headers, "Subject");
                var from = GetHeaderValue(message.Headers, "From");
                var emailDate = DateTimeOffset.FromUnixTimeMilliseconds(message.InternalDate).DateTime;

                // ✅ FIX 1: Remove duplicate check - let controller handle it
                // var existingEmail = await _dataService.GetProcessedEmailByGmailIdAsync(message.Id);
                // if (existingEmail != null) continue;

                foreach (var filter in filters.Where(f => f.IsActive))
                {
                    if (MatchesFilter(message, filter))
                    {
                        var extractedData = await ExtractDataFromEmail(message, filter);

                        var processedEmail = new ProcessedEmail
                        {
                            GmailId = message.Id,
                            AccountEmail = accountEmail,
                            Subject = subject,
                            Body = message.Body,
                            FromEmail = ExtractEmailFromHeader(from),
                            Amount = extractedData.ContainsKey("amount") ? Convert.ToDecimal(extractedData["amount"]) : 0,
                            Currency = extractedData.ContainsKey("currency") ? extractedData["currency"].ToString() : "USD",
                            EmailDate = emailDate,
                            FilterId = filter.Id,
                            FilterName = filter.Name,
                            ExtractedData = extractedData,
                            ProcessedAt = DateTime.UtcNow,
                            Metadata = new Dictionary<string, object>
                            {
                                {"filter_name", filter.Name},
                                {"filter_type", filter.Type ?? "unknown"},
                                {"filter_description", filter.Description ?? ""},
                                {"original_from", from},
                                {"processing_timestamp", DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss")},
                                {"platform", extractedData.ContainsKey("platform") ? extractedData["platform"] : "unknown"}
                            }
                        };

                        processedEmails.Add(processedEmail);
                        // ✅ FIX 2: Remove immediate save - let controller handle it
                        // await _dataService.SaveProcessedEmailAsync(processedEmail);
                        break;
                    }
                }
            }

            return processedEmails;
        }

        public async Task<Dictionary<string, object>> ExtractDataFromEmail(GmailMessage message, EmailFilter filter)
        {
            var extractedData = new Dictionary<string, object>();
            var content = $"{GetHeaderValue(message.Headers, "Subject")} {message.Body}";

            if (filter.ExtractionRules != null && filter.ExtractionRules.Any())
            {
                // ✅ FIX 3: Handle Dictionary<string, ExtractionRule> correctly
                foreach (var ruleKvp in filter.ExtractionRules)
                {
                    var key = ruleKvp.Key;
                    var rule = ruleKvp.Value;

                    var value = ExtractFieldValue(content, rule);
                    if (!string.IsNullOrEmpty(value))
                    {
                        extractedData[key] = value;
                    }
                }
            }
            else
            {
                // Fallback to legacy extraction
                extractedData["amount"] = await ExtractAmountFromContentAsync(content);
                extractedData["currency"] = await ExtractCurrencyFromContentAsync(content);
            }

            return extractedData;
        }

       

        public bool MatchesFilter(GmailMessage message, EmailFilter filter)
        {
            var subject = GetHeaderValue(message.Headers, "Subject");
            var from = GetHeaderValue(message.Headers, "From");
            var body = message.Body ?? "";

            // Check custom conditions
            if (filter.Conditions != null && filter.Conditions.Any())
            {
                var matchedConditions = 0;
                var totalConditions = filter.Conditions.Count(c => !string.IsNullOrWhiteSpace(c.Value));

                foreach (var condition in filter.Conditions.Where(c => !string.IsNullOrWhiteSpace(c.Value)))
                {
                    bool conditionMatches = condition.Type switch
                    {
                        "sender" => CheckCondition(from, condition),
                        "title" or "subject" => CheckCondition(subject, condition),
                        "body" => CheckCondition(body, condition),
                        _ => false
                    };

                    if (conditionMatches)
                    {
                        matchedConditions++;
                        if (filter.MatchCondition == "any" || filter.MatchCondition == "or")
                        {
                            return true; // OR logic - any condition matches
                        }
                    }
                }

                // AND logic - all conditions must match
                if (filter.MatchCondition == "all" || string.IsNullOrEmpty(filter.MatchCondition))
                {
                    return matchedConditions == totalConditions;
                }
            }

            // Check legacy BodyContains for backward compatibility
            if (!string.IsNullOrEmpty(filter.BodyContains))
            {
                var combinedContent = $"{subject} {body}";
                return combinedContent.Contains(filter.BodyContains, StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }

        private bool CheckCondition(string content, FilterCondition condition)
        {
            if (string.IsNullOrEmpty(content) || string.IsNullOrEmpty(condition.Value))
                return false;

            return condition.Operator switch
            {
                "contains" => content.Contains(condition.Value, StringComparison.OrdinalIgnoreCase),
                "not_contains" => !content.Contains(condition.Value, StringComparison.OrdinalIgnoreCase),
                "equals" => content.Equals(condition.Value, StringComparison.OrdinalIgnoreCase),
                "starts_with" => content.StartsWith(condition.Value, StringComparison.OrdinalIgnoreCase),
                "ends_with" => content.EndsWith(condition.Value, StringComparison.OrdinalIgnoreCase),
                _ => content.Contains(condition.Value, StringComparison.OrdinalIgnoreCase)
            };
        }

        // ✅ FIX 5: Update method signature to accept ExtractionRule directly
        private string ExtractFieldValue(string content, ExtractionRule rule)
        {
            if (rule.Patterns == null || !rule.Patterns.Any())
                return string.Empty;

            foreach (var pattern in rule.Patterns)
            {
                try
                {
                    var matches = Regex.Matches(content, pattern, RegexOptions.IgnoreCase | RegexOptions.Multiline);
                    foreach (Match match in matches)
                    {
                        if (match.Success && match.Groups.Count > 1)
                        {
                            var value = match.Groups[1].Value.Trim();
                            if (!string.IsNullOrEmpty(value))
                            {
                                // Clean up numeric values based on rule key or pattern
                                if (IsAmountPattern(rule.Key, pattern))
                                {
                                    value = value.Replace(",", "").Replace(" ", "");
                                    if (decimal.TryParse(value, out var numValue) && numValue > 0)
                                    {
                                        return numValue.ToString();
                                    }
                                }
                                else
                                {
                                    return value;
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // Log regex error but continue with other patterns
                    Console.WriteLine($"Regex error for pattern '{pattern}': {ex.Message}");
                }
            }

            return string.Empty;
        }

        // ✅ FIX 6: Helper method to identify amount patterns
        private bool IsAmountPattern(string ruleKey, string pattern)
        {
            var amountKeywords = new[] { "amount", "Amount", "tiền", "price", "total", "sum", "cost" };
            return amountKeywords.Any(keyword =>
                ruleKey.Contains(keyword, StringComparison.OrdinalIgnoreCase) ||
                pattern.Contains(keyword, StringComparison.OrdinalIgnoreCase));
        }

        public Task<decimal> ExtractAmountFromContentAsync(string content)
        {
            if (string.IsNullOrEmpty(content))
                return Task.FromResult(0m);

            var patterns = new[]
            {
                @"Amount[:\s]*\$?([0-9,]+\.?\d*)\s*(USD|VND|EUR|GBP|HKD)?",
                @"Total[:\s]*\$?([0-9,]+\.?\d*)",
                @"Số tiền[:\s]*\$?([0-9,]+\.?\d*)",
                @"\$([0-9,]+\.?\d*)",
                @"([\d,.]+)\s*(USD|VND|EUR|VNĐ|đ|GBP|HKD)",
                @"(USD|VND|EUR|VNĐ|đ|GBP|HKD)\s*([\d,.]+)"
            };

            foreach (var pattern in patterns)
            {
                var matches = Regex.Matches(content, pattern, RegexOptions.IgnoreCase);
                foreach (Match match in matches)
                {
                    string amountStr = "";

                    // Handle different group patterns
                    if (match.Groups.Count >= 3 && !string.IsNullOrEmpty(match.Groups[1].Value))
                    {
                        amountStr = match.Groups[1].Value;
                    }
                    else if (match.Groups.Count >= 2 && !string.IsNullOrEmpty(match.Groups[1].Value))
                    {
                        amountStr = match.Groups[1].Value;
                    }
                    else if (match.Groups.Count >= 3 && !string.IsNullOrEmpty(match.Groups[2].Value))
                    {
                        amountStr = match.Groups[2].Value;
                    }

                    amountStr = amountStr.Replace(",", "").Replace(" ", "");
                    if (decimal.TryParse(amountStr, out var amount))
                    {
                        if (amount > 0 && amount < 10_000_000)
                            return Task.FromResult(amount);
                    }
                }
            }

            return Task.FromResult(0m);
        }

        public Task<string> ExtractCurrencyFromContentAsync(string content)
        {
            if (string.IsNullOrEmpty(content))
                return Task.FromResult("USD");

            var currencyPatterns = new Dictionary<string, string[]>
            {
                {"USD", new[] {@"\$", @"USD", @"dollar", @"usd"}},
                {"EUR", new[] {@"€", @"EUR", @"euro", @"eur"}},
                {"GBP", new[] {@"£", @"GBP", @"pound", @"gbp"}},
                {"JPY", new[] {@"¥", @"JPY", @"yen", @"jpy"}},
                {"VND", new[] {@"VND", @"vnđ", @"đ", @"dong", @"vnd"}},
                {"CAD", new[] {@"CAD", @"cad"}},
                {"AUD", new[] {@"AUD", @"aud"}},
                {"CHF", new[] {@"CHF", @"chf"}},
                {"CNY", new[] {@"CNY", @"cny", @"yuan"}},
                {"INR", new[] {@"INR", @"inr", @"rupee"}},
                {"HKD", new[] {@"HKD", @"hkd"}},
            };

            foreach (var currency in currencyPatterns)
            {
                foreach (var pattern in currency.Value)
                {
                    if (Regex.IsMatch(content, pattern, RegexOptions.IgnoreCase))
                    {
                        return Task.FromResult(currency.Key);
                    }
                }
            }

            return Task.FromResult("USD");
        }

        private string GetHeaderValue(List<GmailHeader> headers, string headerName)
        {
            return headers?.FirstOrDefault(h =>
                h.Name.Equals(headerName, StringComparison.OrdinalIgnoreCase))?.Value ?? "";
        }

        private string ExtractEmailFromHeader(string fromHeader)
        {
            if (string.IsNullOrEmpty(fromHeader))
                return "";

            var emailMatch = Regex.Match(fromHeader, @"([a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,})");
            return emailMatch.Success ? emailMatch.Groups[1].Value : fromHeader;
        }
    }
}