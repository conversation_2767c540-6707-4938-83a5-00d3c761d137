﻿using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    /// <summary>
    /// Main DataService that orchestrates core services (without AutoScan to avoid circular dependency)
    /// AutoScan will be accessed directly via IAutoScanService
    /// </summary>
    public class DataService : IDataService
    {
        private readonly IAccountService _accountService;
        private readonly IFilterService _filterService;
        private readonly IEmailService _emailService;
        private readonly IStatsService _statsService;
        private readonly ILogger<DataService> _logger;

        // ✅ REMOVED IAutoScanService để tránh circular dependency
        public DataService(
            IAccountService accountService,
            IFilterService filterService,
            IEmailService emailService,
            IStatsService statsService,
            ILogger<DataService> logger)
        {
            _accountService = accountService;
            _filterService = filterService;
            _emailService = emailService;
            _statsService = statsService;
            _logger = logger;

            _logger.LogInformation("🔧 DataService initialized without AutoScan dependency");
        }

        #region Account Management - Delegated to AccountService
        public async Task SaveAccountAsync(GmailAccount account)
            => await _accountService.SaveAccountAsync(account);

        public async Task<GmailAccount?> GetAccountAsync(string accountId)
            => await _accountService.GetAccountAsync(accountId);

        public async Task<List<GmailAccount>> GetAllAccountsAsync()
            => await _accountService.GetAllAccountsAsync();

        public async Task<List<GmailAccount>> GetActiveAccountsAsync()
            => await _accountService.GetActiveAccountsAsync();

        public async Task UpdateAccountAsync(GmailAccount account)
            => await _accountService.UpdateAccountAsync(account);

        public async Task DeleteAccountAsync(string accountId)
            => await _accountService.DeleteAccountAsync(accountId);
        #endregion

        #region Filter Management - Delegated to FilterService
        public async Task SaveFilterAsync(EmailFilter filter)
            => await _filterService.SaveFilterAsync(filter);

        public async Task<EmailFilter?> GetFilterAsync(string filterId)
            => await _filterService.GetFilterAsync(filterId);

        public async Task<List<EmailFilter>> GetAllFiltersAsync()
            => await _filterService.GetAllFiltersAsync();

        public async Task<List<EmailFilter>> GetActiveFiltersAsync()
            => await _filterService.GetActiveFiltersAsync();

        public async Task UpdateFilterAsync(EmailFilter filter)
            => await _filterService.UpdateFilterAsync(filter);

        public async Task DeleteFilterAsync(string filterId)
            => await _filterService.DeleteFilterAsync(filterId);
        #endregion

        #region Email Management - Delegated to EmailService
        public async Task SaveProcessedEmailAsync(ProcessedEmail email)
            => await _emailService.SaveProcessedEmailAsync(email);

        public async Task SaveProcessedEmailsBatchAsync(List<ProcessedEmail> emails)
            => await _emailService.SaveProcessedEmailsBatchAsync(emails);

        public async Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId)
            => await _emailService.GetProcessedEmailAsync(emailId);

        public async Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId)
            => await _emailService.GetProcessedEmailByGmailIdAsync(gmailId);

        public async Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync()
            => await _emailService.GetAllProcessedEmailsAsync();

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail)
            => await _emailService.GetProcessedEmailsByAccountAsync(accountEmail);

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountIdAsync(string accountId)
            => await _emailService.GetProcessedEmailsByAccountIdAsync(accountId);

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate)
            => await _emailService.GetProcessedEmailsByDateRangeAsync(fromDate, toDate);

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId)
            => await _emailService.GetProcessedEmailsByFilterAsync(filterId);

        public async Task<List<ProcessedEmail>> GetRecentProcessedEmailsAsync(int days = 30)
            => await _emailService.GetRecentProcessedEmailsAsync(days);

        public async Task<bool> EmailExistsAsync(string gmailId)
            => await _emailService.EmailExistsAsync(gmailId);

        public async Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds)
            => await _emailService.GetExistingGmailIdsAsync(gmailIds);

        public async Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page,
            int pageSize,
            string? accountEmail = null,
            string? filterId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null)
            => await _emailService.GetProcessedEmailsPagedAsync(page, pageSize, accountEmail, filterId, fromDate, toDate);
        #endregion

        #region Statistics - Delegated to StatsService
        public async Task<DashboardStats> GetDashboardStatsAsync()
            => await _statsService.GetDashboardStatsAsync();

        public async Task<Dictionary<string, CurrencyStats>> GetCurrencyStatsAsync(int days = 30)
            => await _statsService.GetCurrencyStatsAsync(days);

        public async Task<ProcessingStatsResult> GetProcessingStatsAsync(DateTime? fromDate = null, DateTime? toDate = null)
            => await _statsService.GetProcessingStatsAsync(fromDate, toDate);
        #endregion

    }
}