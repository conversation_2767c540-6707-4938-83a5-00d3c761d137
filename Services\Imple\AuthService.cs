﻿using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using System.Text.Json;

namespace LSB.SellerMailTracker.API.Services
{
    public class AuthService : IAuthService
    {
        private readonly IGmailService _gmailService;
        private readonly IDataService _dataService;
        private readonly IUserService _userService;
        private readonly ILogger<AuthService> _logger;

        public AuthService(
            IGmailService gmailService,
            IDataService dataService,
            IUserService userService,
            ILogger<AuthService> logger)
        {
            _gmailService = gmailService;
            _dataService = dataService;
            _userService = userService;
            _logger = logger;
        }

        public async Task<AuthUrlResponse> CreateAuthUrlAsync(CreateAuthUrlRequest request)
        {
            try
            {
                _logger.LogInformation("📥 CreateAuthUrl called with: {Request}", JsonSerializer.Serialize(request));

                // Validate input
                if (string.IsNullOrWhiteSpace(request.SellerInfo?.Name))
                {
                    return new AuthUrlResponse
                    {
                        Success = false,
                        Error = "Seller name is required",
                        Message = "Vui lòng nhập tên seller"
                    };
                }

                // Create pending account
                var pendingAccount = CreatePendingAccount(request.SellerInfo);

                // Save pending account to database
                await _dataService.SaveAccountAsync(pendingAccount);
                _logger.LogInformation("✅ Pending account saved: {AccountId} for seller: {SellerName}",
                    pendingAccount.Id, pendingAccount.SellerInfo.Name);

                // Create auth URL with account ID in state
                var stateData = CreateStateData(pendingAccount.Id, request.SellerInfo);
                _logger.LogInformation("📤 StateData: {StateData}", JsonSerializer.Serialize(stateData));

                var authUrl = await _gmailService.GetAuthorizationUrlAsync(stateData);

                return new AuthUrlResponse
                {
                    Success = true,
                    AuthUrl = authUrl,
                    AccountId = pendingAccount.Id,
                    Message = $"Seller '{request.SellerInfo.Name}' đã được tạo và đang chờ xác thực",
                    SellerInfo = request.SellerInfo,
                    Status = "pending",
                    Instructions = new[]
                    {
                        "1. Seller đã được thêm vào hệ thống với trạng thái 'Đang chờ'",
                        "2. Chia sẻ link này với seller",
                        "3. Seller click vào link để đăng nhập Gmail",
                        "4. Sau khi đăng nhập, tài khoản sẽ chuyển sang trạng thái 'Đã kết nối'"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ CreateAuthUrl error: {Message}", ex.Message);
                return new AuthUrlResponse
                {
                    Success = false,
                    Error = ex.Message,
                    Message = "Không thể tạo seller và link xác thực"
                };
            }
        }

        public async Task<OAuth2CallbackResult> ProcessOAuth2CallbackAsync(string code, string state, string? error = null)
        {
            try
            {
                // Check for OAuth errors
                if (!string.IsNullOrEmpty(error))
                {
                    var errorMessage = GetErrorMessage(error);
                    return new OAuth2CallbackResult
                    {
                        Success = false,
                        Message = errorMessage,
                        Details = "Vui lòng thử lại hoặc liên hệ admin nếu vấn đề tiếp tục.",
                        Error = error
                    };
                }

                if (string.IsNullOrEmpty(code))
                {
                    return new OAuth2CallbackResult
                    {
                        Success = false,
                        Message = "Không nhận được mã xác thực từ Google",
                        Details = "Vui lòng thử lại quá trình đăng nhập."
                    };
                }

                _logger.LogInformation("🚀 OAuth2Callback - Processing auth callback...");

                // Process OAuth and get tokens
                var oauthResult = await _gmailService.ProcessAuthCallbackAsync(code, state);

                _logger.LogInformation("🔍 OAuth result: Email: {Email}, SellerInfo: {SellerInfo}",
                    oauthResult.Email, oauthResult.SellerInfo?.Name ?? "NULL");

                // Try to extract account ID from state to update existing pending account
                var existingAccountId = ExtractAccountIdFromState(state);

                var finalAccount = await UpdateOrCreateAccountAsync(existingAccountId, oauthResult);

                // Save final account
                await _dataService.SaveAccountAsync(finalAccount);
                _logger.LogInformation("✅ Final account saved successfully!");

                // Verify save
                await VerifyAccountSaveAsync(finalAccount.Id);

                // Return success response
                var successMessage = $"🎉 Tài khoản {finalAccount.Email} đã được kết nối thành công!";
                var successDetails = $"Chào mừng {finalAccount.SellerInfo?.Name ?? "bạn"}! Tài khoản Gmail đã được kích hoạt trong hệ thống.";

                return new OAuth2CallbackResult
                {
                    Success = true,
                    Message = successMessage,
                    Details = successDetails,
                    AccountEmail = finalAccount.Email,
                    SellerName = finalAccount.SellerInfo?.Name
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ OAuth callback error: {Message}", ex.Message);
                return new OAuth2CallbackResult
                {
                    Success = false,
                    Message = "Lỗi khi xử lý xác thực",
                    Details = ex.Message,
                    Error = ex.Message
                };
            }
        }

        public async Task<AuthUrlResponse> GetAuthLinkAsync()
        {
            try
            {
                var authUrl = await _gmailService.GetAuthorizationUrlAsync();

                return new AuthUrlResponse
                {
                    Success = true,
                    AuthUrl = authUrl,
                    Message = "Nhấn vào link để đăng nhập Gmail",
                    Instructions = new[]
                    {
                        "1. Nhấn vào link bên dưới",
                        "2. Đăng nhập tài khoản Google của bạn",
                        "3. Cấp quyền truy cập Gmail cho ứng dụng",
                        "4. Chờ hệ thống tự động thêm tài khoản"
                    }
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating auth link: {Message}", ex.Message);
                return new AuthUrlResponse
                {
                    Success = false,
                    Error = ex.Message,
                    Message = "Không thể tạo link xác thực"
                };
            }
        }

        public async Task<bool> RefreshTokenAsync(string accountId)
        {
            try
            {
                return await _gmailService.RefreshTokenAsync(accountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing token for account {AccountId}: {Message}", accountId, ex.Message);
                throw;
            }
        }

        #region Private Helper Methods

        private GmailAccount CreatePendingAccount(SellerInfo sellerInfo)
        {
            return new GmailAccount
            {
                Id = Guid.NewGuid().ToString(),
                Email = "", // Will be filled when OAuth completes
                DisplayName = sellerInfo.Name,
                AccessToken = "",
                RefreshToken = "",
                TokenExpiry = DateTime.UtcNow, // Will be updated after OAuth
                IsActive = false, // Not active until OAuth completes
                Status = "pending", // Pending status
                CreatedAt = DateTime.UtcNow,
                LastSyncAt = null,
                SellerInfo = new SellerInfo
                {
                    Name = sellerInfo.Name.Trim(),
                    Location = sellerInfo.Location?.Trim(),
                    Note = sellerInfo.Note?.Trim(),
                    CreatedAt = DateTime.UtcNow
                }
            };
        }

        private object CreateStateData(string accountId, SellerInfo sellerInfo)
        {
            return new
            {
                accountId = accountId,
                sellerInfo = sellerInfo,
                redirectUrl = "/OAuth2Callback", // This will be handled by the controller
                timestamp = DateTime.UtcNow
            };
        }

        private string? ExtractAccountIdFromState(string state)
        {
            if (string.IsNullOrEmpty(state))
                return null;

            try
            {
                var decodedState = System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(state));
                using var stateDocument = JsonDocument.Parse(decodedState);
                var root = stateDocument.RootElement;

                if (root.TryGetProperty("accountId", out var accountIdElement))
                {
                    var accountId = accountIdElement.GetString();
                    _logger.LogInformation("🔍 Found existing account ID in state: {AccountId}", accountId);
                    return accountId;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning("⚠️ Could not extract account ID from state: {Message}", ex.Message);
            }

            return null;
        }

        private async Task<GmailAccount> UpdateOrCreateAccountAsync(string? existingAccountId, GmailAccount oauthResult)
        {
            if (!string.IsNullOrEmpty(existingAccountId))
            {
                var existingAccount = await _dataService.GetAccountAsync(existingAccountId);
                if (existingAccount != null)
                {
                    _logger.LogInformation("✅ Updating existing pending account: {SellerName}", existingAccount.SellerInfo?.Name);

                    // Update existing account with OAuth data
                    existingAccount.Email = oauthResult.Email;
                    existingAccount.AccessToken = oauthResult.AccessToken;
                    existingAccount.RefreshToken = oauthResult.RefreshToken;
                    existingAccount.TokenExpiry = oauthResult.TokenExpiry;
                    existingAccount.IsActive = true;
                    existingAccount.Status = "active";
                    existingAccount.LastSyncAt = DateTime.UtcNow;

                    // Keep existing seller info if OAuth result doesn't have it
                    if (oauthResult.SellerInfo != null && !string.IsNullOrEmpty(oauthResult.SellerInfo.Name))
                    {
                        existingAccount.SellerInfo = oauthResult.SellerInfo;
                        existingAccount.DisplayName = oauthResult.SellerInfo.Name;
                    }

                    return existingAccount;
                }
                else
                {
                    _logger.LogWarning("⚠️ Pending account not found, creating new one");
                }
            }
            else
            {
                _logger.LogInformation("ℹ️ No account ID in state, using OAuth result as-is");
            }

            return oauthResult;
        }

        private async Task VerifyAccountSaveAsync(string accountId)
        {
            var savedAccount = await _dataService.GetAccountAsync(accountId);
            if (savedAccount != null)
            {
                _logger.LogInformation("✅ Verification successful: Email: {Email}, Status: {Status}, SellerInfo: {SellerName}",
                    savedAccount.Email, savedAccount.Status, savedAccount.SellerInfo?.Name ?? "NULL");
            }
        }

        private string GetErrorMessage(string error)
        {
            return error switch
            {
                "access_denied" => "Bạn đã từ chối cấp quyền truy cập Gmail",
                "invalid_request" => "Yêu cầu xác thực không hợp lệ",
                "unauthorized_client" => "Ứng dụng chưa được cấp phép",
                "unsupported_response_type" => "Loại phản hồi không được hỗ trợ",
                "invalid_scope" => "Quyền truy cập không hợp lệ",
                "server_error" => "Lỗi máy chủ Google",
                "temporarily_unavailable" => "Dịch vụ tạm thời không khả dụng",
                _ => $"Lỗi xác thực: {error}"
            };
        }

        #endregion

        #region JWT Authentication Methods

        public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
        {
            return await _userService.LoginAsync(request);
        }

        public async Task<ApiResponse<LoginResponse>> RegisterAsync(RegisterRequest request)
        {
            return await _userService.RegisterAsync(request);
        }

        public async Task<ApiResponse<LoginResponse>> RefreshJwtTokenAsync(RefreshTokenRequest request)
        {
            return await _userService.RefreshTokenAsync(request);
        }

        public async Task<ApiResponse<object>> LogoutAsync(string userId)
        {
            return await _userService.LogoutAsync(userId);
        }

        #endregion
    }
}