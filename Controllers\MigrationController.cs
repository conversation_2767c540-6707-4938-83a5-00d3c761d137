using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Services.Imple;
using LSB.SellerMailTracker.API.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize(Roles = "Admin")] // Only admin can perform migration
    public class MigrationController : ControllerBase
    {
        private readonly IFirebaseService _firebaseService;
        private readonly ILogger<MigrationController> _logger;
        private readonly ILoggerFactory _loggerFactory;

        public MigrationController(IFirebaseService firebaseService, ILogger<MigrationController> logger, ILoggerFactory loggerFactory)
        {
            _firebaseService = firebaseService;
            _logger = logger;
            _loggerFactory = loggerFactory;
        }

        /// <summary>
        /// Migrate all data from JSON files to Firebase
        /// </summary>
        [HttpPost("migrate-all")]
        public async Task<IActionResult> MigrateAll()
        {
            try
            {
                var migrationLogger = _loggerFactory.CreateLogger<DataMigrationService>();
                var migrationService = new DataMigrationService(_firebaseService, migrationLogger);
                var success = await migrationService.MigrateAllDataAsync();

                if (success)
                {
                    return Ok(ApiResponse<object>.SuccessResult(null, "Migration completed successfully"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Migration completed with errors"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during migration");
                return BadRequest(ApiResponse<object>.ErrorResult("Migration failed: " + ex.Message));
            }
        }

        /// <summary>
        /// Migrate Gmail accounts only
        /// </summary>
        [HttpPost("migrate-accounts")]
        public async Task<IActionResult> MigrateAccounts()
        {
            try
            {
                var migrationLogger = _loggerFactory.CreateLogger<DataMigrationService>();
                var migrationService = new DataMigrationService(_firebaseService, migrationLogger);
                var success = await migrationService.MigrateGmailAccountsAsync();

                if (success)
                {
                    return Ok(ApiResponse<object>.SuccessResult(null, "Gmail accounts migration completed"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Gmail accounts migration failed"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Gmail accounts migration");
                return BadRequest(ApiResponse<object>.ErrorResult("Migration failed: " + ex.Message));
            }
        }

        /// <summary>
        /// Migrate email filters only
        /// </summary>
        [HttpPost("migrate-filters")]
        public async Task<IActionResult> MigrateFilters()
        {
            try
            {
                var migrationLogger = _loggerFactory.CreateLogger<DataMigrationService>();
                var migrationService = new DataMigrationService(_firebaseService, migrationLogger);
                var success = await migrationService.MigrateEmailFiltersAsync();

                if (success)
                {
                    return Ok(ApiResponse<object>.SuccessResult(null, "Email filters migration completed"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Email filters migration failed"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during email filters migration");
                return BadRequest(ApiResponse<object>.ErrorResult("Migration failed: " + ex.Message));
            }
        }

        /// <summary>
        /// Migrate processed emails only
        /// </summary>
        [HttpPost("migrate-emails")]
        public async Task<IActionResult> MigrateEmails()
        {
            try
            {
                var migrationLogger = _loggerFactory.CreateLogger<DataMigrationService>();
                var migrationService = new DataMigrationService(_firebaseService, migrationLogger);
                var success = await migrationService.MigrateProcessedEmailsAsync();

                if (success)
                {
                    return Ok(ApiResponse<object>.SuccessResult(null, "Processed emails migration completed"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Processed emails migration failed"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during processed emails migration");
                return BadRequest(ApiResponse<object>.ErrorResult("Migration failed: " + ex.Message));
            }
        }

        /// <summary>
        /// Migrate auto scan settings only
        /// </summary>
        [HttpPost("migrate-settings")]
        public async Task<IActionResult> MigrateSettings()
        {
            try
            {
                var migrationLogger = _loggerFactory.CreateLogger<DataMigrationService>();
                var migrationService = new DataMigrationService(_firebaseService, migrationLogger);
                var success = await migrationService.MigrateAutoScanSettingsAsync();

                if (success)
                {
                    return Ok(ApiResponse<object>.SuccessResult(null, "Auto scan settings migration completed"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Auto scan settings migration failed"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during auto scan settings migration");
                return BadRequest(ApiResponse<object>.ErrorResult("Migration failed: " + ex.Message));
            }
        }

        /// <summary>
        /// Create backup of Firebase data to JSON files
        /// </summary>
        [HttpPost("backup")]
        public async Task<IActionResult> BackupData()
        {
            try
            {
                var migrationLogger = _loggerFactory.CreateLogger<DataMigrationService>();
                var migrationService = new DataMigrationService(_firebaseService, migrationLogger);
                var success = await migrationService.BackupDataToJsonAsync();

                if (success)
                {
                    return Ok(ApiResponse<object>.SuccessResult(null, "Backup created successfully"));
                }
                else
                {
                    return BadRequest(ApiResponse<object>.ErrorResult("Backup creation failed"));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during backup creation");
                return BadRequest(ApiResponse<object>.ErrorResult("Backup failed: " + ex.Message));
            }
        }

        /// <summary>
        /// Get Firebase connection status
        /// </summary>
        [HttpGet("status")]
        public async Task<IActionResult> GetStatus()
        {
            try
            {
                // Test Firebase connection by getting users count
                var users = await _firebaseService.GetAllUsersAsync();
                var accounts = await _firebaseService.GetAllAccountsAsync();
                var filters = await _firebaseService.GetAllFiltersAsync();
                var emails = await _firebaseService.GetAllProcessedEmailsAsync();

                var status = new
                {
                    FirebaseConnected = true,
                    UsersCount = users.Count,
                    AccountsCount = accounts.Count,
                    FiltersCount = filters.Count,
                    EmailsCount = emails.Count,
                    LastChecked = DateTime.UtcNow
                };

                return Ok(ApiResponse<object>.SuccessResult(status, "Firebase status retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking Firebase status");
                var status = new
                {
                    FirebaseConnected = false,
                    Error = ex.Message,
                    LastChecked = DateTime.UtcNow
                };
                return Ok(ApiResponse<object>.SuccessResult(status, "Firebase connection failed"));
            }
        }

        /// <summary>
        /// Test Firebase operations
        /// </summary>
        [HttpPost("test")]
        public async Task<IActionResult> TestFirebase()
        {
            try
            {
                var testResults = new List<string>();

                // Test user operations
                try
                {
                    var testUser = new Models.User
                    {
                        Id = Guid.NewGuid().ToString(),
                        Email = "<EMAIL>",
                        Name = "Firebase Test User",
                        PasswordHash = "test-hash",
                        Role = "User",
                        CreatedAt = DateTime.UtcNow,
                        IsActive = true
                    };

                    await _firebaseService.CreateUserAsync(testUser);
                    var retrievedUser = await _firebaseService.GetUserAsync(testUser.Id);
                    await _firebaseService.DeleteUserAsync(testUser.Id);

                    testResults.Add("✅ User operations: PASSED");
                }
                catch (Exception ex)
                {
                    testResults.Add($"❌ User operations: FAILED - {ex.Message}");
                }

                // Test account operations
                try
                {
                    var testAccount = new Models.GmailAccount
                    {
                        Id = Guid.NewGuid().ToString(),
                        Email = "<EMAIL>",
                        DisplayName = "Test Account",
                        RefreshToken = "test-refresh",
                        AccessToken = "test-access",
                        TokenExpiry = DateTime.UtcNow.AddHours(1),
                        CreatedAt = DateTime.UtcNow,
                        IsActive = true,
                        Status = "active"
                    };

                    await _firebaseService.CreateAccountAsync(testAccount);
                    var retrievedAccount = await _firebaseService.GetAccountAsync(testAccount.Id);
                    await _firebaseService.DeleteAccountAsync(testAccount.Id);

                    testResults.Add("✅ Account operations: PASSED");
                }
                catch (Exception ex)
                {
                    testResults.Add($"❌ Account operations: FAILED - {ex.Message}");
                }

                return Ok(ApiResponse<object>.SuccessResult(testResults, "Firebase test completed"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during Firebase test");
                return BadRequest(ApiResponse<object>.ErrorResult("Firebase test failed: " + ex.Message));
            }
        }
    }
}
