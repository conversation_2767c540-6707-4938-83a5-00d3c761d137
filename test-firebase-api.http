### Firebase Migration API Tests

@baseUrl = https://localhost:7126
@contentType = application/json
@adminToken = your-admin-jwt-token-here

### 1. Login as admin to get token
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "admin123",
  "rememberMe": false
}

### 2. Check Firebase status
GET {{baseUrl}}/api/migration/status
Authorization: Bearer {{adminToken}}

### 3. Test Firebase operations
POST {{baseUrl}}/api/migration/test
Authorization: Bearer {{adminToken}}

### 4. Migrate Gmail accounts from JSON to Firebase
POST {{baseUrl}}/api/migration/migrate-accounts
Authorization: Bearer {{adminToken}}

### 5. Migrate email filters from JSON to Firebase
POST {{baseUrl}}/api/migration/migrate-filters
Authorization: Bearer {{adminToken}}

### 6. Migrate processed emails from JSON to Firebase
POST {{baseUrl}}/api/migration/migrate-emails
Authorization: Bearer {{adminToken}}

### 7. Migrate auto scan settings from JSON to Firebase
POST {{baseUrl}}/api/migration/migrate-settings
Authorization: Bearer {{adminToken}}

### 8. Migrate all data from JSON to Firebase
POST {{baseUrl}}/api/migration/migrate-all
Authorization: Bearer {{adminToken}}

### 9. Create backup of Firebase data to JSON
POST {{baseUrl}}/api/migration/backup
Authorization: Bearer {{adminToken}}

### 10. Test user registration (should save to Firebase)
POST {{baseUrl}}/api/auth/register
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "test123",
  "confirmPassword": "test123",
  "name": "Firebase Test User"
}

### 10. Test user login (should read from Firebase)
POST {{baseUrl}}/api/auth/login
Content-Type: {{contentType}}

{
  "email": "<EMAIL>",
  "password": "test123",
  "rememberMe": false
}

### 11. Test protected endpoint with Firebase user
GET {{baseUrl}}/api/user/profile
Authorization: Bearer your-firebase-user-token-here

### 12. Test OAuth account creation (should save to Firebase)
POST {{baseUrl}}/api/auth/auth-url
Content-Type: {{contentType}}

{
  "sellerInfo": {
    "name": "Firebase Test Seller",
    "description": "Testing Firebase integration"
  }
}
