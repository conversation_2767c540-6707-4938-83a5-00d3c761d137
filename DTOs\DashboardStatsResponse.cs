﻿namespace LSB.SellerMailTracker.API.DTOs
{
    public class DashboardStatsResponse
    {
        public int TotalAccounts { get; set; }
        public int ActiveAccounts { get; set; }
        public int ActiveFilters { get; set; }
        public int TotalEmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime? LastProcessedDate { get; set; }
        public string LastProcessedRelativeTime => LastProcessedDate?.ToString("dd/MM/yyyy HH:mm") ?? "Chưa xử lý";
        public int EmailsToday { get; set; }
        public int EmailsThisWeek { get; set; }
        public int EmailsThisMonth { get; set; }
        public decimal AmountToday { get; set; }
        public List<CurrencySummary> TopCurrencies { get; set; } = new();

        public List<ProcessedEmailResponse> RecentEmails { get; set; } = new();
    }
}