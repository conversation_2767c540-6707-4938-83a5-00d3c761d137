# JWT Authentication API Guide

## Tổng quan
API này cung cấp hệ thống xác thực JWT (JSON Web Token) với các chức năng đăng ký, đăng nhập, là<PERSON> mới token và đăng xuất.

## Endpoints

### 1. Đăng ký tài khoản mới
**POST** `/api/auth/register`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "confirmPassword": "password123",
  "name": "Tên người dùng"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Đăng ký thành công",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "base64-encoded-refresh-token",
    "expiresAt": "2024-01-01T12:00:00Z",
    "user": {
      "id": "user-id",
      "email": "<EMAIL>",
      "name": "Tên người dùng",
      "role": "User",
      "createdAt": "2024-01-01T11:00:00Z",
      "lastLoginAt": "2024-01-01T11:00:00Z"
    }
  }
}
```

### 2. Đăng nhập
**POST** `/api/auth/login`

**Request Body:**
```json
{
  "email": "<EMAIL>",
  "password": "password123",
  "rememberMe": false
}
```

**Response:** Giống như response của đăng ký

### 3. Làm mới token
**POST** `/api/auth/refresh-jwt-token`

**Request Body:**
```json
{
  "refreshToken": "base64-encoded-refresh-token"
}
```

**Response:** Giống như response của đăng nhập với token mới

### 4. Đăng xuất
**POST** `/api/auth/logout`

**Headers:**
```
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "success": true,
  "message": "Đăng xuất thành công"
}
```

### 5. Lấy thông tin profile (Protected)
**GET** `/api/user/profile`

**Headers:**
```
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "success": true,
  "message": "Profile retrieved successfully",
  "data": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "Tên người dùng",
    "role": "User",
    "createdAt": "2024-01-01T11:00:00Z",
    "lastLoginAt": "2024-01-01T12:00:00Z"
  }
}
```

### 6. Test JWT Authentication
**GET** `/api/user/test`

**Headers:**
```
Authorization: Bearer your-jwt-token
```

**Response:**
```json
{
  "message": "JWT Authentication is working!",
  "user": {
    "id": "user-id",
    "email": "<EMAIL>",
    "name": "Tên người dùng",
    "role": "User"
  },
  "claims": [...],
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 7. Admin Test (Admin only)
**GET** `/api/user/admin-test`

**Headers:**
```
Authorization: Bearer admin-jwt-token
```

## Tài khoản mặc định
- **Email:** <EMAIL>
- **Password:** admin123
- **Role:** Admin

## Cách sử dụng

1. **Đăng ký hoặc đăng nhập** để nhận JWT token
2. **Sử dụng token** trong header `Authorization: Bearer <token>` cho các API protected
3. **Làm mới token** khi token hết hạn (1 giờ)
4. **Đăng xuất** để vô hiệu hóa refresh token

## Cấu hình JWT
Trong `appsettings.json`:
```json
{
  "Jwt": {
    "Key": "YourSecretKeyHere_MustBeAtLeast32Characters",
    "Issuer": "GmailProcessor",
    "Audience": "GmailProcessorClient"
  }
}
```

## Cách chạy API

1. **Chạy project:**
   ```bash
   dotnet run
   ```

2. **Truy cập Swagger UI:**
   - URL: https://localhost:7126/swagger
   - Tại đây bạn có thể test tất cả các endpoints

3. **Test với Postman:**
   - Import file `JWT_API_Postman_Collection.json`
   - Chạy các request theo thứ tự

4. **Test với VS Code REST Client:**
   - Sử dụng file `test-jwt-api.http`
   - Cài extension "REST Client" trong VS Code

## Quy trình test cơ bản

1. **Đăng nhập admin:**
   ```bash
   POST /api/auth/login
   {
     "email": "<EMAIL>",
     "password": "admin123"
   }
   ```

2. **Copy token từ response và test:**
   ```bash
   GET /api/user/test
   Authorization: Bearer <your-token>
   ```

3. **Test admin endpoint:**
   ```bash
   GET /api/user/admin-test
   Authorization: Bearer <admin-token>
   ```

## Lưu ý bảo mật
- Access token có thời hạn 1 giờ
- Refresh token có thời hạn 7 ngày
- Mật khẩu được hash bằng SHA256 + salt
- Sử dụng HTTPS trong production
- Trong production, thay đổi JWT secret key trong appsettings.json
