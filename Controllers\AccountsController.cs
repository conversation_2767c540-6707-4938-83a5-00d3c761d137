﻿using LSB.SellerMailTracker.API.Services.Interfaces;
using LSB.SellerMailTracker.API.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AccountsController : ControllerBase
    {
        private readonly IDataService _dataService;

        public AccountsController(IDataService dataService)
        {
            _dataService = dataService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAccounts()
        {
            try
            {
                var accounts = await _dataService.GetAllAccountsAsync();
                var response = accounts.ToResponse();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{accountId}")]
        public async Task<IActionResult> DeleteAccount(string accountId)
        {
            try
            {
                await _dataService.DeleteAccountAsync(accountId);
                return Ok(new { message = "Account deleted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}