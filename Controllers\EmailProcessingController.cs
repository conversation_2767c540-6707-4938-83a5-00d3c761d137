﻿using Microsoft.AspNetCore.Mvc;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using System.Text.Json;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class EmailProcessingController : ControllerBase
    {
        private readonly IDataService _dataService;
        private readonly IGmailService _gmailService;
        private readonly IEmailFilterService _filterService;
        private readonly ITemplateService _templateService;
        private readonly ILogger<EmailProcessingController> _logger;

        public EmailProcessingController(
            IDataService dataService,
            IGmailService gmailService,
            IEmailFilterService filterService,
            ITemplateService templateService,
            ILogger<EmailProcessingController> logger)
        {
            _dataService = dataService;
            _gmailService = gmailService;
            _filterService = filterService;
            _templateService = templateService;
            _logger = logger;
        }

        [HttpPost("process-single-account")]
        public async Task<ActionResult<ProcessAccountResponse>> ProcessSingleAccount(
            [FromBody] ProcessAccountRequest request)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                // Add detailed request logging
                _logger.LogInformation($"🚀 Starting email processing for account: {request.AccountId}");
                _logger.LogInformation($"📋 Request details:");
                _logger.LogInformation($"   - AccountId: {request.AccountId}");
                _logger.LogInformation($"   - FromDate: {request.FromDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Not specified"}");
                _logger.LogInformation($"   - ToDate: {request.ToDate?.ToString("yyyy-MM-dd HH:mm:ss") ?? "Not specified"}");
                _logger.LogInformation($"   - FilterIds count: {request.FilterIds?.Count ?? 0}");
                _logger.LogInformation($"   - TemplateFilterIds count: {request.TemplateFilterIds?.Count ?? 0}");

                if (request.FilterIds?.Any() == true)
                {
                    _logger.LogInformation($"   - FilterIds: [{string.Join(", ", request.FilterIds)}]");
                }

                if (request.TemplateFilterIds?.Any() == true)
                {
                    _logger.LogInformation($"   - TemplateFilterIds: [{string.Join(", ", request.TemplateFilterIds)}]");
                }

                // Get account
                var account = await _dataService.GetAccountAsync(request.AccountId);
                if (account == null)
                {
                    return NotFound(new ProcessAccountResponse
                    {
                        AccountId = request.AccountId,
                        Success = false,
                        ErrorMessage = $"Account {request.AccountId} not found",
                        ProcessingStartTime = startTime,
                        ProcessingEndTime = DateTime.UtcNow
                    });
                }

                _logger.LogInformation($"📧 Processing account: {account.Email}");

                // Check token expiry before processing
                if (DateTime.UtcNow >= account.TokenExpiry)
                {
                    _logger.LogWarning($"⚠️ Token expired for {account.Email}, attempting refresh...");
                    var refreshSuccess = await _gmailService.RefreshTokenAsync(account.Id);
                    if (!refreshSuccess)
                    {
                        return StatusCode(500, new ProcessAccountResponse
                        {
                            AccountId = request.AccountId,
                            AccountEmail = account.Email,
                            Success = false,
                            ErrorMessage = $"Failed to refresh token for {account.Email}",
                            ProcessingStartTime = startTime,
                            ProcessingEndTime = DateTime.UtcNow
                        });
                    }
                }

                // Get filters based on request
                var filters = await GetFiltersForProcessing(request);
                _logger.LogInformation($"🎯 Using {filters.Count} filters for processing");

                foreach (var filter in filters)
                {
                    _logger.LogInformation($"📋 Filter: {filter.Name} (Type: {filter.Type}, Active: {filter.IsActive})");
                }

                // Get emails from Gmail API
                var emails = await _gmailService.GetEmailsAsync(
                    account.Id,
                    request.FromDate,
                    request.ToDate
                );

                var totalProcessed = emails.Count;
                _logger.LogInformation($"📧 Retrieved {totalProcessed} emails from Gmail API");

                if (emails.Count == 0)
                {
                    _logger.LogInformation($"⚠️ No emails found for account {account.Email}");
                    return Ok(new ProcessAccountResponse
                    {
                        AccountId = request.AccountId,
                        AccountEmail = account.Email,
                        Success = true,
                        TotalProcessed = 0,
                        NewEmails = 0,
                        SkippedEmails = 0,
                        TotalAmount = 0,
                        ProcessingStartTime = startTime,
                        ProcessingEndTime = DateTime.UtcNow,
                        ProcessedEmails = new List<SimpleEmailResponse>(),
                        CurrencySummaries = new Dictionary<string, SimpleCurrencySummary>()
                    });
                }

                // Apply filters - chỉ lấy emails hợp lệ với filter
                List<ProcessedEmail> processedEmails;

                if (filters.Any())
                {
                    processedEmails = await _filterService.ProcessEmailsAsync(
                        emails,
                        filters,
                        account.Email
                    );
                    _logger.LogInformation($"✅ Filtered {emails.Count} emails → {processedEmails.Count} valid emails matched with {filters.Count} selected filters");
                }
                else
                {
                    var allActiveFilters = await _dataService.GetActiveFiltersAsync();
                    processedEmails = await _filterService.ProcessEmailsAsync(
                        emails,
                        allActiveFilters,
                        account.Email
                    );
                    _logger.LogInformation($"✅ Filtered {emails.Count} emails → {processedEmails.Count} valid emails matched with {allActiveFilters.Count} default active filters");
                }

                // CHỈ lưu các email đã pass qua filter (processedEmails)
                // KHÔNG lưu email không hợp lệ
                var newEmailsCount = 0;
                foreach (var processedEmail in processedEmails)
                {
                    // Áp dụng dấu cho amount dựa trên loại filter
                    ApplyAmountSignBasedOnFilterType(processedEmail, filters);

                    await _dataService.SaveProcessedEmailAsync(processedEmail);
                    newEmailsCount++;
                    _logger.LogInformation($"💾 Saved valid email: {processedEmail.Subject} - {processedEmail.Amount:+#,##0.00;-#,##0.00;0} {processedEmail.Currency}");
                }

                var skippedEmails = totalProcessed - processedEmails.Count;
                _logger.LogInformation($"📊 Summary: {newEmailsCount} valid emails saved, {skippedEmails} emails skipped (not matching filters)");

                var endTime = DateTime.UtcNow;

                var response = new ProcessAccountResponse
                {
                    AccountId = request.AccountId,
                    AccountEmail = account.Email,
                    Success = true,
                    TotalProcessed = totalProcessed,
                    NewEmails = newEmailsCount,
                    SkippedEmails = totalProcessed - processedEmails.Count,
                    TotalAmount = processedEmails.Sum(e => e.Amount),
                    ProcessingStartTime = startTime,
                    ProcessingEndTime = endTime,
                    ProcessedEmails = processedEmails.Select(e => MapToResponse(e)).ToList(),
                    CurrencySummaries = BuildCurrencySummaries(processedEmails),
                    FiltersUsed = filters.Count,
                    FilterDetails = filters.Select(f => new FilterSummary
                    {
                        Id = f.Id,
                        Name = f.Name,
                        Type = f.Type
                    }).ToList()
                };

                _logger.LogInformation($"✅ Completed processing for account {request.AccountId}: {newEmailsCount} new emails, {processedEmails.Count} total processed, {filters.Count} filters used");

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error processing account {request.AccountId}");
                return StatusCode(500, new ProcessAccountResponse
                {
                    AccountId = request.AccountId,
                    Success = false,
                    ErrorMessage = ex.Message,
                    ProcessingStartTime = startTime,
                    ProcessingEndTime = DateTime.UtcNow
                });
            }
        }

        [HttpPost("process-multiple-accounts")]
        public async Task<ActionResult<BatchProcessResponse>> ProcessMultipleAccounts(
            [FromBody] BatchProcessRequest request)
        {
            var startTime = DateTime.UtcNow;
            try
            {
                _logger.LogInformation($"🚀 Starting batch processing for {request.AccountIds.Count} accounts with filters");

                var results = new Dictionary<string, ProcessAccountResponse>();
                var overallStats = new BatchProcessStats();

                var filters = await GetFiltersForProcessing(new ProcessAccountRequest
                {
                    FilterIds = request.FilterIds,
                    TemplateFilterIds = request.TemplateFilterIds
                });
                _logger.LogInformation($"🎯 Using {filters.Count} filters for batch processing");

                foreach (var accountId in request.AccountIds)
                {
                    try
                    {
                        _logger.LogInformation($"📧 [{overallStats.TotalAccounts + 1}/{request.AccountIds.Count}] Processing account: {accountId}");

                        var accountRequest = new ProcessAccountRequest
                        {
                            AccountId = accountId,
                            FromDate = request.FromDate,
                            ToDate = request.ToDate,
                            FilterIds = request.FilterIds,
                            TemplateFilterIds = request.TemplateFilterIds
                        };

                        var singleResult = await ProcessSingleAccountInternal(accountRequest, filters);
                        results[accountId] = singleResult;

                        // Update overall stats
                        overallStats.TotalAccounts++;
                        if (singleResult.Success)
                        {
                            overallStats.SuccessfulAccounts++;
                            overallStats.TotalEmailsProcessed += singleResult.TotalProcessed;
                            overallStats.TotalNewEmails += singleResult.NewEmails;
                            overallStats.TotalAmount += singleResult.TotalAmount;
                        }
                        else
                        {
                            overallStats.FailedAccounts++;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ Error processing account {accountId} in batch");
                        results[accountId] = new ProcessAccountResponse
                        {
                            AccountId = accountId,
                            Success = false,
                            ErrorMessage = ex.Message,
                            ProcessingStartTime = DateTime.UtcNow,
                            ProcessingEndTime = DateTime.UtcNow
                        };
                        overallStats.FailedAccounts++;
                        overallStats.TotalAccounts++;
                    }
                }

                var endTime = DateTime.UtcNow;

                var response = new BatchProcessResponse
                {
                    Success = overallStats.FailedAccounts == 0,
                    ProcessingStartTime = startTime,
                    ProcessingEndTime = endTime,
                    AccountResults = results,
                    OverallStats = overallStats,
                    FiltersUsed = filters.Count,
                    FilterDetails = filters.Select(f => new FilterSummary
                    {
                        Id = f.Id,
                        Name = f.Name,
                        Type = f.Type
                    }).ToList()
                };

                _logger.LogInformation($"✅ Batch processing completed: {overallStats.SuccessfulAccounts}/{overallStats.TotalAccounts} successful, {filters.Count} filters used");

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error in batch processing");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("processed-emails")]
        public async Task<ActionResult<PagedEmailResponse>> GetProcessedEmails(
            [FromQuery] int page = 1,
            [FromQuery] int pageSize = 20,
            [FromQuery] string? accountId = null,
            [FromQuery] string? filterId = null,
            [FromQuery] DateTime? fromDate = null,
            [FromQuery] DateTime? toDate = null)
        {
            try
            {
                var (emails, totalCount) = await _dataService.GetProcessedEmailsPagedAsync(
                    page,
                    pageSize,
                    null,
                    filterId,
                    fromDate,
                    toDate);

                // Apply account filter if provided
                if (!string.IsNullOrEmpty(accountId))
                {
                    var account = await _dataService.GetAccountAsync(accountId);
                    if (account != null)
                    {
                        emails = emails.Where(e => e.AccountEmail.Equals(account.Email, StringComparison.OrdinalIgnoreCase)).ToList();
                        totalCount = emails.Count;
                    }
                }

                var response = new PagedEmailResponse
                {
                    Items = emails.Select(e => MapToResponse(e)).ToList(),
                    TotalItems = totalCount,
                    TotalPages = (int)Math.Ceiling((double)totalCount / pageSize),
                    CurrentPage = page,
                    PageSize = pageSize
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error retrieving processed emails");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("dashboard-stats")]
        public async Task<ActionResult<DashboardResponse>> GetDashboardStats()
        {
            try
            {
                var accounts = await _dataService.GetAllAccountsAsync();
                var filters = await _dataService.GetAllFiltersAsync();
                var recentEmails = await _dataService.GetRecentProcessedEmailsAsync(30);

                var today = DateTime.UtcNow.Date;
                var thisWeek = today.AddDays(-(int)today.DayOfWeek);
                var thisMonth = new DateTime(today.Year, today.Month, 1);

                var emailsToday = recentEmails.Where(e => e.EmailDate.Date == today).ToList();
                var emailsThisWeek = recentEmails.Where(e => e.EmailDate.Date >= thisWeek).ToList();
                var emailsThisMonth = recentEmails.Where(e => e.EmailDate.Date >= thisMonth).ToList();

                var response = new DashboardResponse
                {
                    TotalAccounts = accounts.Count(a => a.IsActive),
                    ActiveFilters = filters.Count(f => f.IsActive),
                    TotalEmailsProcessed = recentEmails.Count,
                    TotalAmount = recentEmails.Sum(e => e.Amount),

                    EmailsToday = emailsToday.Count,
                    EmailsThisWeek = emailsThisWeek.Count,
                    EmailsThisMonth = emailsThisMonth.Count,
                    AmountToday = emailsToday.Sum(e => e.Amount),

                    TopCurrencies = BuildCurrencySummaries(recentEmails).Values.Take(5).ToList(),
                    RecentEmails = recentEmails.Take(10).Select(e => MapToResponse(e)).ToList()
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error retrieving dashboard stats");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // Debug endpoints
        [HttpGet("debug/templates")]
        public ActionResult GetAllTemplates()
        {
            try
            {
                var templates = _templateService.GetEmailTemplates();

                _logger.LogInformation($"📋 Available templates: {templates.Count}");

                var result = templates.Select(t => new
                {
                    Id = t.Id,
                    Name = t.Name,
                    Type = t.Type,
                    Platform = t.Platform,
                    Language = t.Language,
                    ConditionsCount = t.Conditions?.Count ?? 0,
                    Conditions = t.Conditions?.Select(c => new
                    {
                        Type = c.Type,
                        Operator = c.Operator,
                        Value = c.Value
                    }).ToList(),
                    ExtractionRules = t.ExtractionRules?.Select(r => new
                    {
                        Key = r.Key,
                        PatternsCount = r.Value.Patterns?.Count ?? 0,
                        Patterns = r.Value.Patterns
                    }).ToList()
                }).ToList();

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error getting templates");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost("debug/test-template")]
        public async Task<ActionResult> TestTemplate([FromBody] TestTemplateRequest request)
        {
            try
            {
                _logger.LogInformation($"🧪 Testing template: {request.TemplateId}");

                var templateFilter = await GetTemplateFilter(request.TemplateId);

                if (templateFilter == null)
                {
                    return NotFound(new { message = $"Template {request.TemplateId} not found" });
                }

                var result = new
                {
                    TemplateId = request.TemplateId,
                    Filter = new
                    {
                        Id = templateFilter.Id,
                        Name = templateFilter.Name,
                        Type = templateFilter.Type,
                        MatchCondition = templateFilter.MatchCondition,
                        ConditionsCount = templateFilter.Conditions?.Count ?? 0,
                        Conditions = templateFilter.Conditions?.Select(c => new
                        {
                            Type = c.Type,
                            Operator = c.Operator,
                            Value = c.Value
                        }).ToList(),
                        ExtractionRules = templateFilter.ExtractionRules?.Select(r => new
                        {
                            Key = r.Key,
                            PatternsCount = r.Value.Patterns?.Count ?? 0,
                            Patterns = r.Value.Patterns
                        }).ToList()
                    }
                };

                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error testing template {request.TemplateId}");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // Get filters for processing
        private async Task<List<EmailFilter>> GetFiltersForProcessing(ProcessAccountRequest request)
        {
            var filters = new List<EmailFilter>();

            _logger.LogInformation($"🎯 Processing filter request - Custom FilterIds: {request.FilterIds?.Count ?? 0}, Template FilterIds: {request.TemplateFilterIds?.Count ?? 0}");

            // Add custom filters if specified
            if (request.FilterIds?.Any() == true)
            {
                _logger.LogInformation($"🔧 Processing {request.FilterIds.Count} custom filter IDs");

                foreach (var filterId in request.FilterIds)
                {
                    _logger.LogInformation($"   - Looking for custom filter: {filterId}");
                    var filter = await _dataService.GetFilterAsync(filterId);

                    if (filter != null && filter.IsActive)
                    {
                        filters.Add(filter);
                        _logger.LogInformation($"   ✅ Added custom filter: {filter.Name} (ID: {filterId})");
                    }
                    else if (filter == null)
                    {
                        _logger.LogWarning($"   ⚠️ Custom filter not found: {filterId}");
                    }
                    else
                    {
                        _logger.LogWarning($"   ⚠️ Custom filter inactive: {filter.Name} (ID: {filterId})");
                    }
                }
            }

            // Add template filters if specified
            if (request.TemplateFilterIds?.Any() == true)
            {
                _logger.LogInformation($"📋 Processing {request.TemplateFilterIds.Count} template filter IDs");

                foreach (var templateId in request.TemplateFilterIds)
                {
                    _logger.LogInformation($"   - Looking for template: {templateId}");
                    var templateFilter = await GetTemplateFilter(templateId);

                    if (templateFilter != null)
                    {
                        filters.Add(templateFilter);
                        _logger.LogInformation($"   ✅ Added template filter: {templateFilter.Name} (TemplateId: {templateId})");
                    }
                    else
                    {
                        _logger.LogWarning($"   ❌ Template filter not found: {templateId}");
                    }
                }
            }

            _logger.LogInformation($"🎯 Final filter count: {filters.Count}");
            foreach (var filter in filters)
            {
                _logger.LogInformation($"   - {filter.Name} (Type: {filter.Type}, Conditions: {filter.Conditions?.Count ?? 0})");
            }

            return filters;
        }

        // Get template filter - Fixed version với additional validation
        private async Task<EmailFilter?> GetTemplateFilter(string templateId)
        {
            try
            {
                _logger.LogInformation($"🔍 Looking for template with ID: {templateId}");

                // Lấy template từ TemplateService
                var template = _templateService.GetEmailTemplate(templateId);

                if (template == null)
                {
                    _logger.LogWarning($"⚠️ Template not found: {templateId}");
                    return null;
                }

                _logger.LogInformation($"✅ Found template: {template.Name} ({template.Platform})");

                // Convert EmailTemplate thành EmailFilter với validation logic
                var filter = new EmailFilter
                {
                    Id = Guid.NewGuid().ToString(),
                    TemplateId = template.Id,
                    Name = template.Name,
                    Type = template.Type,
                    Description = $"Auto-generated from template: {template.Name}",
                    IsActive = true,
                    // Logic match condition dựa trên platform
                    MatchCondition = template.Platform switch
                    {
                        "PayPal" => "all", // PayPal cần strict vì có nhiều loại email
                        "Payoneer" => "any", // Payoneer ít false positive hơn
                        _ => "any"
                    },
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Conditions = template.Conditions.Select(c => new FilterCondition
                    {
                        Id = Guid.NewGuid().ToString(),
                        Type = c.Type,
                        Operator = c.Operator,
                        Value = c.Value
                    }).ToList(),
                    ExtractionRules = new Dictionary<string, ExtractionRule>(template.ExtractionRules)
                };

                _logger.LogInformation($"✅ Created filter from template: {filter.Name} with {filter.Conditions.Count} conditions, MatchCondition: {filter.MatchCondition}");

                foreach (var condition in filter.Conditions)
                {
                    _logger.LogInformation($"   - Condition: {condition.Type} {condition.Operator} '{condition.Value}'");
                }

                return filter;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error getting template filter {templateId}");
                return null;
            }
        }

        // Private helper method for internal processing
        private async Task<ProcessAccountResponse> ProcessSingleAccountInternal(
            ProcessAccountRequest request,
            List<EmailFilter>? providedFilters = null)
        {
            var startTime = DateTime.UtcNow;

            var account = await _dataService.GetAccountAsync(request.AccountId);
            if (account == null)
            {
                throw new ArgumentException($"Account {request.AccountId} not found");
            }

            _logger.LogInformation($"📧 Internal processing for account: {account.Email}");

            // Check token expiry
            if (DateTime.UtcNow >= account.TokenExpiry)
            {
                _logger.LogWarning($"⚠️ Token expired for {account.Email}, attempting refresh...");
                var refreshSuccess = await _gmailService.RefreshTokenAsync(account.Id);
                if (!refreshSuccess)
                {
                    throw new Exception($"Failed to refresh token for {account.Email}");
                }
            }

            // Use provided filters or get from request
            var filters = providedFilters ?? await GetFiltersForProcessing(request);

            var emails = await _gmailService.GetEmailsAsync(
                account.Id,
                request.FromDate,
                request.ToDate
            );

            var totalProcessed = emails.Count;
            _logger.LogInformation($"📧 Retrieved {totalProcessed} emails from Gmail API");

            if (emails.Count == 0)
            {
                return new ProcessAccountResponse
                {
                    AccountId = request.AccountId,
                    AccountEmail = account.Email,
                    Success = true,
                    TotalProcessed = 0,
                    NewEmails = 0,
                    SkippedEmails = 0,
                    TotalAmount = 0,
                    ProcessingStartTime = startTime,
                    ProcessingEndTime = DateTime.UtcNow,
                    ProcessedEmails = new List<SimpleEmailResponse>(),
                    CurrencySummaries = new Dictionary<string, SimpleCurrencySummary>(),
                    FiltersUsed = filters.Count
                };
            }

            // Apply filters - chỉ lấy emails hợp lệ với filter
            List<ProcessedEmail> processedEmails;

            if (filters.Any())
            {
                processedEmails = await _filterService.ProcessEmailsAsync(
                    emails,
                    filters,
                    account.Email
                );
                _logger.LogInformation($"✅ Filtered {emails.Count} emails → {processedEmails.Count} valid emails with {filters.Count} specific filters");
            }
            else
            {
                var allActiveFilters = await _dataService.GetActiveFiltersAsync();
                processedEmails = await _filterService.ProcessEmailsAsync(
                    emails,
                    allActiveFilters,
                    account.Email
                );
                _logger.LogInformation($"✅ Filtered {emails.Count} emails → {processedEmails.Count} valid emails with {allActiveFilters.Count} default active filters");
            }

            // CHỈ lưu các email đã pass qua filter (processedEmails)
            // KHÔNG lưu email không hợp lệ
            var newEmailsCount = 0;
            foreach (var processedEmail in processedEmails)
            {
                // Áp dụng dấu cho amount dựa trên loại filter
                ApplyAmountSignBasedOnFilterType(processedEmail, filters);

                await _dataService.SaveProcessedEmailAsync(processedEmail);
                newEmailsCount++;
            }

            var skippedEmails = totalProcessed - processedEmails.Count;
            _logger.LogInformation($"📊 Internal processing summary: {newEmailsCount} valid emails saved, {skippedEmails} emails skipped");

            return new ProcessAccountResponse
            {
                AccountId = request.AccountId,
                AccountEmail = account.Email,
                Success = true,
                TotalProcessed = totalProcessed,
                NewEmails = newEmailsCount,
                SkippedEmails = totalProcessed - processedEmails.Count,
                TotalAmount = processedEmails.Sum(e => e.Amount),
                ProcessingStartTime = startTime,
                ProcessingEndTime = DateTime.UtcNow,
                ProcessedEmails = processedEmails.Select(e => MapToResponse(e)).ToList(),
                CurrencySummaries = BuildCurrencySummaries(processedEmails),
                FiltersUsed = filters.Count,
                FilterDetails = filters.Select(f => new FilterSummary
                {
                    Id = f.Id,
                    Name = f.Name,
                    Type = f.Type
                }).ToList()
            };
        }

        private SimpleEmailResponse MapToResponse(ProcessedEmail email)
        {
            return new SimpleEmailResponse
            {
                Id = email.Id,
                GmailId = email.GmailId,
                AccountEmail = email.AccountEmail,
                Subject = email.Subject,
                FromEmail = email.FromEmail,
                Amount = email.Amount,
                Currency = email.Currency,
                EmailDate = email.EmailDate,
                ProcessedAt = email.ProcessedAt,
                FilterId = email.FilterId,
                FilterName = email.FilterName ?? ""
            };
        }

        // Build currency summaries
        private Dictionary<string, SimpleCurrencySummary> BuildCurrencySummaries(List<ProcessedEmail> emails)
        {
            if (!emails.Any())
                return new Dictionary<string, SimpleCurrencySummary>();

            return emails
                .GroupBy(e => e.Currency ?? "Unknown")
                .ToDictionary(g => g.Key, g => new SimpleCurrencySummary
                {
                    Currency = g.Key,
                    TotalAmount = g.Sum(e => e.Amount),
                    EmailCount = g.Count(),
                    AverageAmount = g.Any() ? g.Average(e => e.Amount) : 0,
                    MinAmount = g.Any() ? g.Min(e => e.Amount) : 0,
                    MaxAmount = g.Any() ? g.Max(e => e.Amount) : 0,
                    LastTransactionDate = g.Any() ? g.Max(e => e.EmailDate) : DateTime.MinValue
                });
        }

        // Apply amount sign based on filter type (income = +, outcome = -)
        private void ApplyAmountSignBasedOnFilterType(ProcessedEmail email, List<EmailFilter> filters)
        {
            try
            {
                // Tìm filter đã match với email này
                var matchedFilter = filters.FirstOrDefault(f => f.Id == email.FilterId || f.Name == email.FilterName);

                if (matchedFilter != null)
                {
                    // Áp dụng dấu dựa trên loại filter
                    switch (matchedFilter.Type?.ToLower())
                    {
                        case "income":
                            // Income = dương (+)
                            email.Amount = Math.Abs(email.Amount);
                            _logger.LogDebug($"✅ Applied INCOME sign: +{email.Amount} {email.Currency} for filter '{matchedFilter.Name}'");
                            break;

                        case "outcome":
                            // Outcome = âm (-)
                            email.Amount = -Math.Abs(email.Amount);
                            _logger.LogDebug($"✅ Applied OUTCOME sign: {email.Amount} {email.Currency} for filter '{matchedFilter.Name}'");
                            break;

                        default:
                            // Không xác định được loại, giữ nguyên
                            _logger.LogWarning($"⚠️ Unknown filter type '{matchedFilter.Type}' for email {email.Subject}, keeping original amount: {email.Amount}");
                            break;
                    }
                }
                else
                {
                    _logger.LogWarning($"⚠️ Could not find matched filter for email {email.Subject} (FilterId: {email.FilterId}, FilterName: {email.FilterName})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error applying amount sign for email {email.Subject}");
            }
        }
    }

    // Request/Response DTOs
    public class ProcessAccountRequest
    {
        public string AccountId { get; set; } = "";
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? FilterIds { get; set; } = new();
        public List<string>? TemplateFilterIds { get; set; } = new();
    }

    public class BatchProcessRequest
    {
        public List<string> AccountIds { get; set; } = new();
        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }
        public List<string>? FilterIds { get; set; } = new();
        public List<string>? TemplateFilterIds { get; set; } = new();
    }

    public class ProcessAccountResponse
    {
        public string AccountId { get; set; } = "";
        public string AccountEmail { get; set; } = "";
        public bool Success { get; set; }
        public int TotalProcessed { get; set; }
        public int NewEmails { get; set; }
        public int SkippedEmails { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime ProcessingStartTime { get; set; }
        public DateTime ProcessingEndTime { get; set; }
        public TimeSpan ProcessingDuration => ProcessingEndTime - ProcessingStartTime;
        public List<SimpleEmailResponse> ProcessedEmails { get; set; } = new();
        public Dictionary<string, SimpleCurrencySummary> CurrencySummaries { get; set; } = new();
        public string? ErrorMessage { get; set; }
        public int FiltersUsed { get; set; }
        public List<FilterSummary> FilterDetails { get; set; } = new();
    }

    public class BatchProcessResponse
    {
        public bool Success { get; set; }
        public DateTime ProcessingStartTime { get; set; }
        public DateTime ProcessingEndTime { get; set; }
        public TimeSpan ProcessingDuration => ProcessingEndTime - ProcessingStartTime;
        public Dictionary<string, ProcessAccountResponse> AccountResults { get; set; } = new();
        public BatchProcessStats OverallStats { get; set; } = new();
        public int FiltersUsed { get; set; }
        public List<FilterSummary> FilterDetails { get; set; } = new();
    }

    public class BatchProcessStats
    {
        public int TotalAccounts { get; set; }
        public int SuccessfulAccounts { get; set; }
        public int FailedAccounts { get; set; }
        public int TotalEmailsProcessed { get; set; }
        public int TotalNewEmails { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class FilterSummary
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
    }

    public class SimpleEmailResponse
    {
        public string Id { get; set; } = "";
        public string GmailId { get; set; } = "";
        public string AccountEmail { get; set; } = "";
        public string Subject { get; set; } = "";
        public string FromEmail { get; set; } = "";
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "";
        public DateTime EmailDate { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string FilterId { get; set; } = "";
        public string FilterName { get; set; } = "";
    }

    public class SimpleCurrencySummary
    {
        public string Currency { get; set; } = "";
        public decimal TotalAmount { get; set; }
        public int EmailCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal MinAmount { get; set; }
        public decimal MaxAmount { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }

    public class PagedEmailResponse
    {
        public List<SimpleEmailResponse> Items { get; set; } = new();
        public int TotalItems { get; set; }
        public int TotalPages { get; set; }
        public int CurrentPage { get; set; }
        public int PageSize { get; set; }
    }

    public class DashboardResponse
    {
        public int TotalAccounts { get; set; }
        public int ActiveFilters { get; set; }
        public int TotalEmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }

        public int EmailsToday { get; set; }
        public int EmailsThisWeek { get; set; }
        public int EmailsThisMonth { get; set; }
        public decimal AmountToday { get; set; }

        public List<SimpleCurrencySummary> TopCurrencies { get; set; } = new();
        public List<SimpleEmailResponse> RecentEmails { get; set; } = new();
    }

    public class TestTemplateRequest
    {
        public string TemplateId { get; set; } = "";
    }
}