using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FirebaseAccountService : IAccountService
    {
        private readonly IFirebaseService _firebaseService;
        private readonly ILogger<FirebaseAccountService> _logger;

        public FirebaseAccountService(IFirebaseService firebaseService, ILogger<FirebaseAccountService> logger)
        {
            _firebaseService = firebaseService;
            _logger = logger;
        }

        public async Task SaveAccountAsync(GmailAccount account)
        {
            try
            {
                var existingAccount = await _firebaseService.GetAccountAsync(account.Id);
                if (existingAccount != null)
                {
                    await _firebaseService.UpdateAccountAsync(account);
                    _logger.LogInformation("Account updated: {AccountId} - {Email}", account.Id, account.Email);
                }
                else
                {
                    await _firebaseService.CreateAccountAsync(account);
                    _logger.LogInformation("Account created: {AccountId} - {Email}", account.Id, account.Email);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving account {AccountId}", account.Id);
                throw;
            }
        }

        public async Task<GmailAccount?> GetAccountAsync(string accountId)
        {
            try
            {
                return await _firebaseService.GetAccountAsync(accountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account {AccountId}", accountId);
                return null;
            }
        }

        public async Task<List<GmailAccount>> GetAllAccountsAsync()
        {
            try
            {
                return await _firebaseService.GetAllAccountsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all accounts");
                return new List<GmailAccount>();
            }
        }

        public async Task UpdateAccountAsync(GmailAccount account)
        {
            try
            {
                await _firebaseService.UpdateAccountAsync(account);
                _logger.LogInformation("Account updated: {AccountId} - {Email}", account.Id, account.Email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account {AccountId}", account.Id);
                throw;
            }
        }

        public async Task DeleteAccountAsync(string accountId)
        {
            try
            {
                await _firebaseService.DeleteAccountAsync(accountId);
                _logger.LogInformation("Account deleted: {AccountId}", accountId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting account {AccountId}", accountId);
                throw;
            }
        }

        public async Task<List<GmailAccount>> GetActiveAccountsAsync()
        {
            try
            {
                return await _firebaseService.GetActiveAccountsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active accounts");
                return new List<GmailAccount>();
            }
        }

        public async Task<GmailAccount?> GetAccountByEmailAsync(string email)
        {
            try
            {
                var accounts = await _firebaseService.GetAllAccountsAsync();
                return accounts.FirstOrDefault(a => a.Email.Equals(email, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account by email {Email}", email);
                return null;
            }
        }

        public async Task<bool> AccountExistsAsync(string email)
        {
            try
            {
                var account = await GetAccountByEmailAsync(email);
                return account != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if account exists {Email}", email);
                return false;
            }
        }

        public async Task<List<GmailAccount>> GetAccountsByStatusAsync(string status)
        {
            try
            {
                var accounts = await _firebaseService.GetAllAccountsAsync();
                return accounts.Where(a => a.Status.Equals(status, StringComparison.OrdinalIgnoreCase)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting accounts by status {Status}", status);
                return new List<GmailAccount>();
            }
        }

        public async Task<int> GetAccountCountAsync()
        {
            try
            {
                var accounts = await _firebaseService.GetAllAccountsAsync();
                return accounts.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account count");
                return 0;
            }
        }

        public async Task<int> GetActiveAccountCountAsync()
        {
            try
            {
                var accounts = await _firebaseService.GetActiveAccountsAsync();
                return accounts.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active account count");
                return 0;
            }
        }

        public async Task UpdateAccountTokenAsync(string accountId, string accessToken, string refreshToken, DateTime tokenExpiry)
        {
            try
            {
                var account = await _firebaseService.GetAccountAsync(accountId);
                if (account != null)
                {
                    account.AccessToken = accessToken;
                    account.RefreshToken = refreshToken;
                    account.TokenExpiry = tokenExpiry;
                    
                    await _firebaseService.UpdateAccountAsync(account);
                    _logger.LogInformation("Account tokens updated: {AccountId}", accountId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account tokens {AccountId}", accountId);
                throw;
            }
        }

        public async Task UpdateAccountStatusAsync(string accountId, string status)
        {
            try
            {
                var account = await _firebaseService.GetAccountAsync(accountId);
                if (account != null)
                {
                    account.Status = status;
                    await _firebaseService.UpdateAccountAsync(account);
                    _logger.LogInformation("Account status updated: {AccountId} - {Status}", accountId, status);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account status {AccountId}", accountId);
                throw;
            }
        }

        public async Task UpdateLastSyncAsync(string accountId, DateTime lastSyncAt)
        {
            try
            {
                var account = await _firebaseService.GetAccountAsync(accountId);
                if (account != null)
                {
                    account.LastSyncAt = lastSyncAt;
                    await _firebaseService.UpdateAccountAsync(account);
                    _logger.LogInformation("Account last sync updated: {AccountId} - {LastSync}", accountId, lastSyncAt);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account last sync {AccountId}", accountId);
                throw;
            }
        }
    }
}
