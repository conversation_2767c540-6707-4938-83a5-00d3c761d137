﻿using System.Text.Json.Serialization;

namespace LSB.SellerMailTracker.API.Models
{
    public class EmailFilter
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = "custom"; // custom, template, income, outcome
        public string TemplateId { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public string MatchCondition { get; set; } = "any"; // any, all
        public List<FilterCondition> Conditions { get; set; } = new();
        public GmailFilterOptions? GmailFilters { get; set; }
        public Dictionary<string, ExtractionRule> ExtractionRules { get; set; } = new();
        public string CustomQuery { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // Legacy properties for backward compatibility
        public string BodyContains { get; set; } = string.Empty;

        // ✅ Additional properties for enhanced filtering
        public int Priority { get; set; } = 1;
        public List<string> Keywords { get; set; } = new();
        public List<string> SenderDomains { get; set; } = new();
        public string AmountPattern { get; set; } = string.Empty;
        public string CurrencyPattern { get; set; } = string.Empty;
        public DateTime? LastUsed { get; set; }
        public int UsageCount { get; set; } = 0;
    }

    public class FilterCondition
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Type { get; set; } = string.Empty; // sender, title, body, subject
        public string Operator { get; set; } = string.Empty; // contains, not_contains, equals, starts_with, ends_with, regex
        public string Value { get; set; } = string.Empty;

        // ✅ Additional properties
        public bool CaseSensitive { get; set; } = false;
        public bool IsActive { get; set; } = true;
        public int Priority { get; set; } = 1;
        public string? Description { get; set; }
    }

    public class GmailFilterOptions
    {
        public List<string> Labels { get; set; } = new();
        public string HasAttachment { get; set; } = string.Empty;
        public string IsRead { get; set; } = string.Empty;
        public string IsImportant { get; set; } = string.Empty;
        public string IsStarred { get; set; } = string.Empty;
        public DateRangeFilter? DateRange { get; set; }
    }

    public class DateRangeFilter
    {
        public string Type { get; set; } = string.Empty; // any, custom, last_week, last_month, etc.
        public string Value { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class ExtractionRule
    {
        public List<string> Patterns { get; set; } = new();

        // ✅ Additional properties for enhanced extraction
        public string Key { get; set; } = string.Empty;
        public string DataType { get; set; } = "string"; // string, decimal, date, boolean
        public bool Required { get; set; } = false;
        public string? DefaultValue { get; set; }
        public List<string> PostProcessingRules { get; set; } = new();
        public List<string> ValidationRules { get; set; } = new();
    }

    public class GmailAttachment
    {
        public string AttachmentId { get; set; } = string.Empty;
        public string Filename { get; set; } = string.Empty;
        public string MimeType { get; set; } = string.Empty;
        public long Size { get; set; }
    }

    // ✅ NEW: TestFilterResult class
    /// <summary>
    /// Result of testing a filter against sample email content
    /// </summary>
    public class TestFilterResult
    {
        /// <summary>
        /// Whether the filter matched the sample email
        /// </summary>
        public bool Matches { get; set; }

        /// <summary>
        /// Data extracted from the email if matched
        /// </summary>
        public Dictionary<string, object> ExtractedData { get; set; } = new();

        /// <summary>
        /// Human-readable explanation of the test result
        /// </summary>
        public string Explanation { get; set; } = "";

        /// <summary>
        /// Detailed debug information about the matching process
        /// </summary>
        public List<string> DebugInfo { get; set; } = new();

        /// <summary>
        /// Which specific conditions matched (for debugging)
        /// </summary>
        public List<ConditionMatchResult> MatchedConditions { get; set; } = new();

        /// <summary>
        /// Performance metrics for the test
        /// </summary>
        public TestPerformanceMetrics Performance { get; set; } = new();

        /// <summary>
        /// Any warnings or issues encountered during testing
        /// </summary>
        public List<string> Warnings { get; set; } = new();
    }

    // ✅ NEW: Supporting classes for TestFilterResult
    /// <summary>
    /// Result of individual condition matching
    /// </summary>
    public class ConditionMatchResult
    {
        public string ConditionType { get; set; } = "";
        public string Operator { get; set; } = "";
        public string Value { get; set; } = "";
        public bool Matched { get; set; }
        public string ActualContent { get; set; } = "";
        public string MatchedText { get; set; } = "";
    }

    /// <summary>
    /// Performance metrics for filter testing
    /// </summary>
    public class TestPerformanceMetrics
    {
        public TimeSpan ProcessingTime { get; set; }
        public int ConditionsEvaluated { get; set; }
        public int RegexPatternsApplied { get; set; }
        public int ExtractionRulesExecuted { get; set; }
    }

    // ✅ NEW: Enhanced filter performance and validation models
    /// <summary>
    /// Result of filter configuration validation
    /// </summary>
    public class FilterValidationResult
    {
        /// <summary>
        /// Whether the filter configuration is valid
        /// </summary>
        public bool IsValid { get; set; }

        /// <summary>
        /// List of validation errors found
        /// </summary>
        public List<string> Errors { get; set; } = new();

        /// <summary>
        /// List of validation warnings
        /// </summary>
        public List<string> Warnings { get; set; } = new();

        /// <summary>
        /// Suggestions for improving the filter
        /// </summary>
        public List<string> Suggestions { get; set; } = new();

        /// <summary>
        /// Score indicating filter quality (0-100)
        /// </summary>
        public int QualityScore { get; set; }
    }

    /// <summary>
    /// Performance metrics for a specific filter over time
    /// </summary>
    public class FilterPerformanceMetrics
    {
        public string FilterId { get; set; } = "";
        public string FilterName { get; set; } = "";
        public DateTime FromDate { get; set; }
        public DateTime ToDate { get; set; }

        /// <summary>
        /// Total emails processed by this filter
        /// </summary>
        public int TotalEmailsProcessed { get; set; }

        /// <summary>
        /// Number of emails that matched this filter
        /// </summary>
        public int EmailsMatched { get; set; }

        /// <summary>
        /// Match rate as percentage
        /// </summary>
        public double MatchRate => TotalEmailsProcessed > 0 ? (double)EmailsMatched / TotalEmailsProcessed * 100 : 0;

        /// <summary>
        /// Total amount extracted by this filter
        /// </summary>
        public decimal TotalAmountExtracted { get; set; }

        /// <summary>
        /// Average amount per matched email
        /// </summary>
        public decimal AverageAmount => EmailsMatched > 0 ? TotalAmountExtracted / EmailsMatched : 0;

        /// <summary>
        /// Currency breakdown
        /// </summary>
        public Dictionary<string, decimal> CurrencyBreakdown { get; set; } = new();

        /// <summary>
        /// Platform/source breakdown
        /// </summary>
        public Dictionary<string, int> PlatformBreakdown { get; set; } = new();

        /// <summary>
        /// Average processing time per email (milliseconds)
        /// </summary>
        public double AverageProcessingTimeMs { get; set; }

        /// <summary>
        /// Number of extraction errors encountered
        /// </summary>
        public int ExtractionErrors { get; set; }

        /// <summary>
        /// Most common failure reasons
        /// </summary>
        public Dictionary<string, int> FailureReasons { get; set; } = new();

        /// <summary>
        /// Daily match counts for trend analysis
        /// </summary>
        public Dictionary<DateTime, int> DailyMatches { get; set; } = new();
    }

    // ✅ NEW: Additional filter-related models
    /// <summary>
    /// Filter template for common use cases
    /// </summary>
    public class FilterTemplate
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Category { get; set; } = string.Empty; // payment, refund, invoice, etc.
        public List<FilterCondition> DefaultConditions { get; set; } = new();
        public Dictionary<string, ExtractionRule> DefaultExtractionRules { get; set; } = new();
        public List<string> SupportedPlatforms { get; set; } = new();
        public bool IsBuiltIn { get; set; } = false;
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public int UsageCount { get; set; } = 0;
    }

    /// <summary>
    /// Filter usage statistics
    /// </summary>
    public class FilterUsageStats
    {
        public string FilterId { get; set; } = "";
        public int TotalMatches { get; set; }
        public int TotalProcessed { get; set; }
        public decimal TotalAmountExtracted { get; set; }
        public DateTime? FirstUsed { get; set; }
        public DateTime? LastUsed { get; set; }
        public double AverageProcessingTimeMs { get; set; }
        public int ErrorCount { get; set; }
        public double SuccessRate => TotalProcessed > 0 ? (double)(TotalProcessed - ErrorCount) / TotalProcessed * 100 : 0;
    }

    /// <summary>
    /// Filter import/export model
    /// </summary>
    public class FilterExportModel
    {
        public string Version { get; set; } = "1.0";
        public DateTime ExportedAt { get; set; } = DateTime.UtcNow;
        public string ExportedBy { get; set; } = "";
        public List<EmailFilter> Filters { get; set; } = new();
        public List<FilterTemplate> Templates { get; set; } = new();
        public Dictionary<string, object> Metadata { get; set; } = new();
    }
}