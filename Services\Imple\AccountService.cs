﻿using System.Text.Json;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class AccountService : IAccountService
    {
        private readonly string _dataFolder;
        private readonly string _accountsFile;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly SemaphoreSlim _fileLock = new(1, 1);
        private readonly ILogger<AccountService>? _logger;

        public AccountService(ILogger<AccountService>? logger = null)
        {
            _logger = logger;
            _dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            _accountsFile = Path.Combine(_dataFolder, "accounts.json");

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            Directory.CreateDirectory(_dataFolder);
        }

        public async Task SaveAccountAsync(GmailAccount account)
        {
            await _fileLock.WaitAsync();
            try
            {
                var accounts = await LoadAccountsAsync();
                var existingIndex = accounts.FindIndex(a => a.Id == account.Id);

                if (existingIndex >= 0)
                    accounts[existingIndex] = account;
                else
                    accounts.Add(account);

                await SaveAccountsAsync(accounts);
                _logger?.LogInformation($"Account saved: {account.Email}");
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task<GmailAccount?> GetAccountAsync(string accountId)
        {
            var accounts = await LoadAccountsAsync();
            return accounts.FirstOrDefault(a => a.Id == accountId);
        }

        public async Task<List<GmailAccount>> GetAllAccountsAsync()
        {
            return await LoadAccountsAsync();
        }

        public async Task<List<GmailAccount>> GetActiveAccountsAsync()
        {
            var accounts = await LoadAccountsAsync();
            return accounts.Where(a => a.IsActive).ToList();
        }

        public async Task UpdateAccountAsync(GmailAccount account)
        {
            await SaveAccountAsync(account);
        }

        public async Task DeleteAccountAsync(string accountId)
        {
            await _fileLock.WaitAsync();
            try
            {
                var accounts = await LoadAccountsAsync();
                var removedCount = accounts.RemoveAll(a => a.Id == accountId);

                if (removedCount > 0)
                {
                    await SaveAccountsAsync(accounts);
                    _logger?.LogInformation($"Account deleted: {accountId}");
                }
            }
            finally
            {
                _fileLock.Release();
            }
        }

        private async Task<List<GmailAccount>> LoadAccountsAsync()
        {
            if (!File.Exists(_accountsFile))
                return new List<GmailAccount>();

            try
            {
                var json = await File.ReadAllTextAsync(_accountsFile);
                return JsonSerializer.Deserialize<List<GmailAccount>>(json, _jsonOptions) ?? new List<GmailAccount>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error loading accounts from file");
                return new List<GmailAccount>();
            }
        }

        private async Task SaveAccountsAsync(List<GmailAccount> accounts)
        {
            try
            {
                var json = JsonSerializer.Serialize(accounts, _jsonOptions);
                await File.WriteAllTextAsync(_accountsFile, json);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error saving accounts to file");
                throw;
            }
        }
    }
}