﻿using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IAuthService
    {
        // OAuth2 methods (existing)
        Task<AuthUrlResponse> CreateAuthUrlAsync(CreateAuthUrlRequest request);
        Task<OAuth2CallbackResult> ProcessOAuth2CallbackAsync(string code, string state, string? error = null);
        Task<AuthUrlResponse> GetAuthLinkAsync();
        Task<bool> RefreshTokenAsync(string accountId);

        // JWT Authentication methods (new)
        Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request);
        Task<ApiResponse<LoginResponse>> RegisterAsync(RegisterRequest request);
        Task<ApiResponse<LoginResponse>> RefreshJwtTokenAsync(RefreshTokenRequest request);
        Task<ApiResponse<object>> LogoutAsync(string userId);
    }
}