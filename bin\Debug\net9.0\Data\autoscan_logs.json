[{"id": "5359c451-26a8-4a7a-8d07-5d79f3c0396e", "startTime": "2025-08-05T09:54:33.3339653Z", "endTime": "2025-08-05T09:54:34.6742835Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3403182", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "f7a6f2fb-a453-454b-beaf-fcbf780f5c46", "startTime": "2025-08-05T09:53:31.5264232Z", "endTime": "2025-08-05T09:53:33.2819691Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7555459", "durationFormatted": "1.8s", "statusText": "✅ Success"}, {"id": "89a29560-0ec1-44f8-8ea1-ece445ea559f", "startTime": "2025-08-05T09:52:30.1111674Z", "endTime": "2025-08-05T09:52:31.3980986Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2869312", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "b0e21203-f697-4fcc-bfd5-301eb51052f1", "startTime": "2025-08-05T09:51:28.6040374Z", "endTime": "2025-08-05T09:51:30.0230588Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4190214", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "6af39707-2e6f-4b51-a949-681115c0b9a6", "startTime": "2025-08-05T09:50:27.2681167Z", "endTime": "2025-08-05T09:50:28.5253993Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2572826", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "34d44223-1bb7-4409-9fdd-2e891008ad65", "startTime": "2025-08-05T09:49:25.5602684Z", "endTime": "2025-08-05T09:49:27.1996644Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6393960", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "1c80482e-7a8b-43e2-8482-4b0958f79848", "startTime": "2025-08-05T09:48:23.8114797Z", "endTime": "2025-08-05T09:48:25.5208347Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7093550", "durationFormatted": "1.7s", "statusText": "✅ Success"}, {"id": "********-a60b-405e-bac9-b4e1f4cf5a0e", "startTime": "2025-08-05T09:47:22.4779628Z", "endTime": "2025-08-05T09:47:23.7440195Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2660567", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "1a16deeb-af19-4034-86b9-0418af8c2119", "startTime": "2025-08-05T09:46:21.2158946Z", "endTime": "2025-08-05T09:46:22.4218534Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2059588", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "e0487268-ba73-4e2c-921a-8da15179af27", "startTime": "2025-08-05T09:45:19.8577548Z", "endTime": "2025-08-05T09:45:21.0994239Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2416691", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "55da19ba-434b-4626-93b5-c2e2b05564b4", "startTime": "2025-08-05T09:44:18.1333219Z", "endTime": "2025-08-05T09:44:19.7571692Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6238473", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "4c48c1c6-25cc-4e72-8a05-3317224ea9a4", "startTime": "2025-08-05T09:43:16.6963586Z", "endTime": "2025-08-05T09:43:18.0505101Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3541515", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "b076bb43-0cd5-4bb3-82fc-c08b30ec1a18", "startTime": "2025-08-05T09:42:15.1762053Z", "endTime": "2025-08-05T09:42:16.6380939Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4618886", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "dec5f45c-071d-42a5-93c3-8fb8f348060c", "startTime": "2025-08-05T09:41:13.6741836Z", "endTime": "2025-08-05T09:41:15.0956482Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4214646", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "5b916461-4125-48b8-ac99-7b3745926458", "startTime": "2025-08-05T09:40:12.3223179Z", "endTime": "2025-08-05T09:40:13.6075446Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2852267", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "b7e03d36-0fef-47fa-8ba9-447f572f8909", "startTime": "2025-08-05T09:39:10.7422741Z", "endTime": "2025-08-05T09:39:12.2592426Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5169685", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "a115c1c9-296d-4ea5-9434-47d0dd8ea8f3", "startTime": "2025-08-05T09:38:09.2301489Z", "endTime": "2025-08-05T09:38:10.6399209Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4097720", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "a2e8f7dd-b4d2-4364-bdc6-2dc94eee8bd4", "startTime": "2025-08-05T09:37:07.7555402Z", "endTime": "2025-08-05T09:37:09.1114071Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3558669", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "611bd5e2-acce-4242-bf0a-dcdebf636127", "startTime": "2025-08-05T09:36:06.3193682Z", "endTime": "2025-08-05T09:36:07.599915Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2805468", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "7078c3a5-5e39-4016-a4a8-a36d208c18e6", "startTime": "2025-08-05T09:35:04.9002768Z", "endTime": "2025-08-05T09:35:06.255076Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3547992", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "2ad87371-bcc9-4711-8706-0856be60084f", "startTime": "2025-08-05T09:34:03.150845Z", "endTime": "2025-08-05T09:34:04.8314559Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6806109", "durationFormatted": "1.7s", "statusText": "✅ Success"}, {"id": "********-2bbd-4fa1-b193-164919b76e7d", "startTime": "2025-08-05T09:33:01.7210893Z", "endTime": "2025-08-05T09:33:03.0592023Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3381130", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "673d2230-4118-4da9-84eb-72fcecb9e43f", "startTime": "2025-08-05T09:31:59.8975436Z", "endTime": "2025-08-05T09:32:01.6947225Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7971789", "durationFormatted": "1.8s", "statusText": "✅ Success"}, {"id": "1176c6e5-aabe-4d40-bd42-9ab6d497dfbf", "startTime": "2025-08-05T09:30:58.3720207Z", "endTime": "2025-08-05T09:30:59.8219054Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4498847", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "a89811f4-14cd-40ab-80e3-bb769f627bff", "startTime": "2025-08-05T09:29:56.914191Z", "endTime": "2025-08-05T09:29:58.2960256Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3818346", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "876f3509-6a26-45ee-a235-9557fbec21d9", "startTime": "2025-08-05T09:28:55.1662771Z", "endTime": "2025-08-05T09:28:56.8364394Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6701623", "durationFormatted": "1.7s", "statusText": "✅ Success"}, {"id": "3a14041d-1ad5-44e3-b7b3-547dd14f569f", "startTime": "2025-08-05T09:27:53.0553671Z", "endTime": "2025-08-05T09:27:55.1047511Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:02.0493840", "durationFormatted": "2.0s", "statusText": "✅ Success"}, {"id": "e0fc9fbe-5ff8-42cb-8493-3cc1faae9eb8", "startTime": "2025-08-05T09:26:51.7380182Z", "endTime": "2025-08-05T09:26:52.9523583Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2143401", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "fd515be3-3554-4e59-97ba-e0d1a6b2db57", "startTime": "2025-08-05T09:25:50.407334Z", "endTime": "2025-08-05T09:25:51.6748037Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2674697", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "********-93c1-41cf-aabc-84c36b092b3b", "startTime": "2025-08-05T09:24:49.0698098Z", "endTime": "2025-08-05T09:24:50.3259087Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2560989", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "c87f9192-4e76-4c45-8a83-faffb38e8703", "startTime": "2025-08-05T09:23:47.7981843Z", "endTime": "2025-08-05T09:23:49.0284607Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2302764", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "16d730cd-eb8a-4167-922f-79e1a37dacee", "startTime": "2025-08-05T09:22:46.4054518Z", "endTime": "2025-08-05T09:22:47.7384107Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3329589", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "61a7ab87-11a7-431e-a27d-700776b182e8", "startTime": "2025-08-05T09:21:45.0679903Z", "endTime": "2025-08-05T09:21:46.3354402Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2674499", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "39a95a83-fb97-45d4-baee-33c5c4791582", "startTime": "2025-08-05T09:20:43.5479288Z", "endTime": "2025-08-05T09:20:45.0110551Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4631263", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "4a58db3e-23fb-4af7-b550-9dd46dbb9564", "startTime": "2025-08-05T09:19:42.1728985Z", "endTime": "2025-08-05T09:19:43.4778069Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3049084", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "01d847df-fc94-4a47-950f-3ebbc65430d4", "startTime": "2025-08-05T09:18:40.7987991Z", "endTime": "2025-08-05T09:18:42.1141409Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3153418", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "8b5a8f8e-c9d0-4980-8308-6e6852ab91ae", "startTime": "2025-08-05T09:17:39.1412925Z", "endTime": "2025-08-05T09:17:40.742848Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6015555", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "d3d69778-a054-4f3f-ba74-6aff283ae408", "startTime": "2025-08-05T09:16:37.3436851Z", "endTime": "2025-08-05T09:16:39.1162102Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7725251", "durationFormatted": "1.8s", "statusText": "✅ Success"}, {"id": "e2251627-c62c-47de-8e64-d83322868d0c", "startTime": "2025-08-05T09:15:35.944809Z", "endTime": "2025-08-05T09:15:37.2823027Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3374937", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "********-acb7-43d3-9425-ede6859c8071", "startTime": "2025-08-05T09:14:34.6581062Z", "endTime": "2025-08-05T09:14:35.9074413Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2493351", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "2ba745e2-c96c-4dec-9544-85551376439a", "startTime": "2025-08-05T09:13:32.8114003Z", "endTime": "2025-08-05T09:13:34.4603997Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6489994", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "17d49ca4-5321-400c-9afb-e7c10f5e8030", "startTime": "2025-08-05T09:12:31.4385429Z", "endTime": "2025-08-05T09:12:32.7508592Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3123163", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "aed8102c-6c73-4359-8d2e-18b53b466c26", "startTime": "2025-08-05T09:11:30.0410458Z", "endTime": "2025-08-05T09:11:31.3429714Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3019256", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "2371f7cc-c493-4ba1-a0f6-9b8f5efa4572", "startTime": "2025-08-05T09:10:28.5482541Z", "endTime": "2025-08-05T09:10:29.9709602Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4227061", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "5fc25733-4eee-4cb5-a79a-b696c4c4a319", "startTime": "2025-08-05T09:09:27.1784396Z", "endTime": "2025-08-05T09:09:28.4750598Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2966202", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "a9eeaae6-9d15-40de-b97e-b210e49b0c20", "startTime": "2025-08-05T09:08:25.6388392Z", "endTime": "2025-08-05T09:08:27.107697Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4688578", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "d0e0e7de-3322-4ffc-af84-9085eacb72f8", "startTime": "2025-08-05T09:07:23.9857253Z", "endTime": "2025-08-05T09:07:25.5752812Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5895559", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "94299d61-38e5-4458-95dd-e894713253b7", "startTime": "2025-08-05T09:06:21.7550384Z", "endTime": "2025-08-05T09:06:23.8800487Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:02.1250103", "durationFormatted": "2.1s", "statusText": "✅ Success"}, {"id": "28f9f0a0-d380-46fb-afeb-10463370a731", "startTime": "2025-08-05T09:05:20.4341169Z", "endTime": "2025-08-05T09:05:21.6792068Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2450899", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "c7b2d9cf-2ade-4dcc-afdb-0eace9134802", "startTime": "2025-08-05T09:04:19.1595754Z", "endTime": "2025-08-05T09:04:20.3738326Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2142572", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "7d423c38-7d74-4d13-9699-9378f16bfd6f", "startTime": "2025-08-05T09:03:17.6136741Z", "endTime": "2025-08-05T09:03:19.0898353Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4761612", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "b6109809-7be3-48cf-8f23-7fea743a4dad", "startTime": "2025-08-05T09:02:14.5000384Z", "endTime": "2025-08-05T09:02:17.4750881Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:02.9750497", "durationFormatted": "3.0s", "statusText": "✅ Success"}, {"id": "8a3d62f3-0503-4419-b948-cc124702e4cc", "startTime": "2025-08-05T09:01:12.3723054Z", "endTime": "2025-08-05T09:01:14.413938Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:02.0416326", "durationFormatted": "2.0s", "statusText": "✅ Success"}, {"id": "50dc1cbd-1776-47b9-a426-2c838fcfce83", "startTime": "2025-08-05T09:00:10.7341339Z", "endTime": "2025-08-05T09:00:12.2807498Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5466159", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "********-a92f-4e21-ab6e-d584e556b939", "startTime": "2025-08-05T08:59:08.8702271Z", "endTime": "2025-08-05T08:59:10.6186775Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7484504", "durationFormatted": "1.7s", "statusText": "✅ Success"}, {"id": "3da7f177-5ebc-4ef7-a6a0-c7e7163c6a2f", "startTime": "2025-08-05T08:58:06.8141522Z", "endTime": "2025-08-05T08:58:08.7349801Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.9208279", "durationFormatted": "1.9s", "statusText": "✅ Success"}, {"id": "8ae3910f-bc0f-4cae-86b4-5c41fc248803", "startTime": "2025-08-05T08:57:05.3373958Z", "endTime": "2025-08-05T08:57:06.6572415Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3198457", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "2ad6713b-66b0-43f1-b44e-733aea0d8133", "startTime": "2025-08-05T08:56:03.6780888Z", "endTime": "2025-08-05T08:56:05.276452Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5983632", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "dba119ef-2ae0-407b-8894-3fb4b8ea1909", "startTime": "2025-08-05T08:55:02.4038499Z", "endTime": "2025-08-05T08:55:03.6280233Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2241734", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "ccb6ae34-753c-4b48-b3ad-cf90358af59a", "startTime": "2025-08-05T08:54:01.0004844Z", "endTime": "2025-08-05T08:54:02.3188165Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3183321", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "67ed7e60-1902-4b37-9d04-11326151132e", "startTime": "2025-08-05T08:52:59.5195263Z", "endTime": "2025-08-05T08:53:00.9201468Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4006205", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "04f53476-d55f-49eb-b774-a70391237aa4", "startTime": "2025-08-05T08:51:58.1921708Z", "endTime": "2025-08-05T08:51:59.4239281Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2317573", "durationFormatted": "1.2s", "statusText": "✅ Success"}, {"id": "44857fff-14e3-443f-9c38-eb167f7c605f", "startTime": "2025-08-05T08:50:56.813368Z", "endTime": "2025-08-05T08:50:58.1405512Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3271832", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "ca1f3bc7-0657-4275-97dd-7521cc4421c8", "startTime": "2025-08-05T08:49:55.40008Z", "endTime": "2025-08-05T08:49:56.7485405Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3484605", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "afffecb6-7078-430f-b031-6f5053282250", "startTime": "2025-08-05T08:48:53.8505384Z", "endTime": "2025-08-05T08:48:55.3295086Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4789702", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "e231f27f-c3e1-4a47-b98b-e34861e15712", "startTime": "2025-08-05T08:47:52.0657131Z", "endTime": "2025-08-05T08:47:53.7338129Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6680998", "durationFormatted": "1.7s", "statusText": "✅ Success"}, {"id": "9908dca8-c1d1-45be-bef4-1c31e34bdaa4", "startTime": "2025-08-05T08:46:50.2794898Z", "endTime": "2025-08-05T08:46:52.038089Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7585992", "durationFormatted": "1.8s", "statusText": "✅ Success"}, {"id": "a9eb5455-34b9-48ad-98de-0f685e9d608e", "startTime": "2025-08-05T08:45:48.5854868Z", "endTime": "2025-08-05T08:45:50.2192806Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6337938", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "a8faa599-37cb-4b38-bd71-f88ba6f58e36", "startTime": "2025-08-05T08:44:47.0219618Z", "endTime": "2025-08-05T08:44:48.4806056Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4586438", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "93ffdb14-e085-4931-815e-cca1df5aae3b", "startTime": "2025-08-05T08:43:45.6692003Z", "endTime": "2025-08-05T08:43:46.9194817Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2502814", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "4530a262-884b-4397-ad71-88386856fddb", "startTime": "2025-08-05T08:42:44.2933713Z", "endTime": "2025-08-05T08:42:45.6060239Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3126526", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "80d49a3d-6260-4446-8977-20b6d8282838", "startTime": "2025-08-05T08:41:42.9435913Z", "endTime": "2025-08-05T08:41:44.2069427Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2633514", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "50dc6e0b-d47a-40b7-861e-************", "startTime": "2025-08-05T08:40:41.5200613Z", "endTime": "2025-08-05T08:40:42.8817498Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3616885", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "793e63f0-88e1-49d0-b3c5-38fd2797e9d2", "startTime": "2025-08-05T08:39:40.1567895Z", "endTime": "2025-08-05T08:39:41.4585956Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3018061", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "7e164b5f-c3f6-4ab3-b502-f7dea6a58059", "startTime": "2025-08-05T08:38:38.7820778Z", "endTime": "2025-08-05T08:38:40.0924503Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3103725", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "b18bc89e-6f25-4dd6-8794-f61f9aefee74", "startTime": "2025-08-05T08:37:37.43099Z", "endTime": "2025-08-05T08:37:38.7107394Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2797494", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "ba42c967-0d7a-45bc-be93-19f9021a5d16", "startTime": "2025-08-05T08:36:35.9345859Z", "endTime": "2025-08-05T08:36:37.363328Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4287421", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "5e923729-05b3-45fa-a49e-1d202e8bff53", "startTime": "2025-08-05T08:35:34.5608842Z", "endTime": "2025-08-05T08:35:35.8463923Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2855081", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "ad1b55e2-a498-4be5-bde3-e5efa5e3ba3c", "startTime": "2025-08-05T08:34:33.0851284Z", "endTime": "2025-08-05T08:34:34.4873216Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4021932", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "369b4f07-ae8b-490a-809e-a73899947619", "startTime": "2025-08-05T08:33:31.6901236Z", "endTime": "2025-08-05T08:33:32.9580004Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.2678768", "durationFormatted": "1.3s", "statusText": "✅ Success"}, {"id": "ed48758f-9f82-4339-94a7-efcb16bb46ac", "startTime": "2025-08-05T08:32:29.4781082Z", "endTime": "2025-08-05T08:32:31.4518508Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.9737426", "durationFormatted": "2.0s", "statusText": "✅ Success"}, {"id": "a3afad39-c5b4-4342-a571-c44414ec61ac", "startTime": "2025-08-05T08:31:27.7872544Z", "endTime": "2025-08-05T08:31:29.2209421Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4336877", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "f796cd6c-3f18-4820-8470-ae3c72842041", "startTime": "2025-08-05T08:30:26.2179173Z", "endTime": "2025-08-05T08:30:27.7026514Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4847341", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "605f335d-8adc-47fb-9f63-e32c0876b36f", "startTime": "2025-08-05T08:29:24.6880024Z", "endTime": "2025-08-05T08:29:26.1407839Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4527815", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "2e57e2dd-e7d7-438d-956a-ae532cdd5db3", "startTime": "2025-08-05T08:28:23.1605315Z", "endTime": "2025-08-05T08:28:24.5899718Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4294403", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "654462b5-665d-4275-9e55-c76b84977544", "startTime": "2025-08-05T08:27:21.5434342Z", "endTime": "2025-08-05T08:27:23.0593582Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5159240", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "ec3c816c-2503-4039-aee1-1589c9be8c1d", "startTime": "2025-08-05T08:26:19.8376932Z", "endTime": "2025-08-05T08:26:21.4801506Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.6424574", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "0b7fef58-6642-465e-8f01-0928aa39c24b", "startTime": "2025-08-05T08:25:18.3711524Z", "endTime": "2025-08-05T08:25:19.7744358Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4032834", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "84917e70-7780-4879-a303-b91b116eccbc", "startTime": "2025-08-05T08:24:16.5242315Z", "endTime": "2025-08-05T08:24:18.2666456Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7424141", "durationFormatted": "1.7s", "statusText": "✅ Success"}, {"id": "bb5850e3-79d8-497b-ae2e-e93912ec1833", "startTime": "2025-08-05T08:23:14.9859636Z", "endTime": "2025-08-05T08:23:16.4566278Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4706642", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "b770d77e-ac66-40f2-b04c-5d5a652ae78c", "startTime": "2025-08-05T08:22:13.4920687Z", "endTime": "2025-08-05T08:22:14.9064988Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4144301", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "e2638463-54f1-40c1-a478-b8c7b91a3364", "startTime": "2025-08-05T08:21:12.0437017Z", "endTime": "2025-08-05T08:21:13.4226503Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.3789486", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "635fb2f3-0fe8-4c91-b3a3-a793cea2c6a5", "startTime": "2025-08-05T08:20:10.2142639Z", "endTime": "2025-08-05T08:20:11.975123Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.7608591", "durationFormatted": "1.8s", "statusText": "✅ Success"}, {"id": "8a1a9aac-4098-4e05-829b-9eb749f56d84", "startTime": "2025-08-05T08:19:08.6740946Z", "endTime": "2025-08-05T08:19:10.1179313Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4438367", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "4bb21b9a-db3e-46ac-813a-37d23de4ee63", "startTime": "2025-08-05T08:18:07.097585Z", "endTime": "2025-08-05T08:18:08.586941Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4893560", "durationFormatted": "1.5s", "statusText": "✅ Success"}, {"id": "d62c1e0f-d068-4c94-9626-a2b8bc2f2cbc", "startTime": "2025-08-05T08:17:05.4410275Z", "endTime": "2025-08-05T08:17:07.016521Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5754935", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "1eb33c54-f8af-472f-add0-589f1300596b", "startTime": "2025-08-05T08:16:03.1803499Z", "endTime": "2025-08-05T08:16:05.4111215Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:02.2307716", "durationFormatted": "2.2s", "statusText": "✅ Success"}, {"id": "159dd8d9-2dd1-4037-b64d-c83b83d2f169", "startTime": "2025-08-05T08:15:01.4290711Z", "endTime": "2025-08-05T08:15:03.0092393Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5801682", "durationFormatted": "1.6s", "statusText": "✅ Success"}, {"id": "903e4ec3-c981-4b98-b852-77c884128005", "startTime": "2025-08-05T08:13:59.9280868Z", "endTime": "2025-08-05T08:14:01.3647911Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.4367043", "durationFormatted": "1.4s", "statusText": "✅ Success"}, {"id": "fe7381d0-543c-404d-bd9d-e155bbc91e01", "startTime": "2025-08-05T08:12:58.3291054Z", "endTime": "2025-08-05T08:12:59.8616997Z", "success": true, "accountsConfigured": 3, "accountsProcessed": 3, "emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Successful: 1/3 accounts. Errors: <EMAIL>: Failed to refresh <NAME_EMAIL>; <EMAIL>: Failed to refresh <NAME_EMAIL>", "metadata": {"successfulAccounts": 1, "failedAccounts": 2, "configuredAccounts": 3, "filtersUsed": 9, "accountResults": {"5e7be7cd-a7c8-4b3f-92d9-cae4b47298c1": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "5e951a44-18c7-4c7c-8a98-0a6dd82631ea": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": "Failed to refresh <NAME_EMAIL>"}, "7249b44b-e71e-44ce-a82e-0f252cfcdcc6": {"emailsProcessed": 0, "totalAmount": 0, "errorMessage": null}}}, "triggerType": "auto", "usedFilters": [], "usedTemplateFilters": [], "emailsByAccount": {}, "amountsByAccount": {}, "duration": "00:00:01.5325943", "durationFormatted": "1.5s", "statusText": "✅ Success"}]