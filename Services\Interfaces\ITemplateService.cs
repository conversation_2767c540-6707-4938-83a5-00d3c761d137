﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface ITemplateService
    {
        List<EmailTemplate> GetEmailTemplates();
        EmailTemplate? GetEmailTemplate(string templateId);
        Task<EmailFilter> CreateSmartIncomeFilter();
        Task<EmailFilter> CreateSmartOutcomeFilter();
        Task ApplyTemplateToFilter(EmailFilter filter, string templateId);
        Task ApplySmartRulesToFilter(EmailFilter filter, string type);
        List<string> GetSupportedPlatforms();
    }

    public class EmailTemplate
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty; // income, outcome
        public string Name { get; set; } = string.Empty;
        public string Language { get; set; } = string.Empty;
        public string Platform { get; set; } = string.Empty;
        public List<FilterCondition> Conditions { get; set; } = new();
        public Dictionary<string, ExtractionRule> ExtractionRules { get; set; } = new();
    }

    public class TestFilterResult
    {
        public bool Matches { get; set; }
        public Dictionary<string, object> ExtractedData { get; set; } = new();
        public string Explanation { get; set; } = string.Empty;
    }
}