using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using System.Text.Json;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class DataMigrationService
    {
        private readonly IFirebaseService _firebaseService;
        private readonly ILogger<DataMigrationService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        public DataMigrationService(IFirebaseService firebaseService, ILogger<DataMigrationService> logger)
        {
            _firebaseService = firebaseService;
            _logger = logger;
            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };
        }

        public async Task<bool> MigrateAllDataAsync()
        {
            try
            {
                _logger.LogInformation("Starting data migration from JSON files to Firebase...");

                var success = true;
                success &= await MigrateGmailAccountsAsync();
                success &= await MigrateEmailFiltersAsync();
                success &= await MigrateProcessedEmailsAsync();
                success &= await MigrateAutoScanSettingsAsync();

                if (success)
                {
                    _logger.LogInformation("Data migration completed successfully!");
                }
                else
                {
                    _logger.LogWarning("Data migration completed with some errors. Check logs for details.");
                }

                return success;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during data migration");
                return false;
            }
        }

        public async Task<bool> MigrateGmailAccountsAsync()
        {
            try
            {
                var accountsFile = "data/accounts.json";
                if (!File.Exists(accountsFile))
                {
                    _logger.LogInformation("No accounts.json file found, skipping Gmail accounts migration");
                    return true;
                }

                _logger.LogInformation("Migrating Gmail accounts...");
                var json = await File.ReadAllTextAsync(accountsFile);
                var accounts = JsonSerializer.Deserialize<List<GmailAccount>>(json, _jsonOptions);

                if (accounts == null || !accounts.Any())
                {
                    _logger.LogInformation("No Gmail accounts to migrate");
                    return true;
                }

                var migratedCount = 0;
                foreach (var account in accounts)
                {
                    try
                    {
                        // Check if account already exists
                        var existingAccount = await _firebaseService.GetAccountAsync(account.Id);
                        if (existingAccount == null)
                        {
                            await _firebaseService.CreateAccountAsync(account);
                            migratedCount++;
                            _logger.LogInformation("Migrated account: {Email}", account.Email);
                        }
                        else
                        {
                            _logger.LogInformation("Account already exists: {Email}", account.Email);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error migrating account: {Email}", account.Email);
                    }
                }

                _logger.LogInformation("Gmail accounts migration completed. Migrated: {Count}/{Total}", 
                    migratedCount, accounts.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating Gmail accounts");
                return false;
            }
        }

        public async Task<bool> MigrateEmailFiltersAsync()
        {
            try
            {
                var filtersFile = "data/filters.json";
                if (!File.Exists(filtersFile))
                {
                    _logger.LogInformation("No filters.json file found, skipping email filters migration");
                    return true;
                }

                _logger.LogInformation("Migrating email filters...");
                var json = await File.ReadAllTextAsync(filtersFile);
                var filters = JsonSerializer.Deserialize<List<EmailFilter>>(json, _jsonOptions);

                if (filters == null || !filters.Any())
                {
                    _logger.LogInformation("No email filters to migrate");
                    return true;
                }

                var migratedCount = 0;
                foreach (var filter in filters)
                {
                    try
                    {
                        // Check if filter already exists
                        var existingFilter = await _firebaseService.GetFilterAsync(filter.Id);
                        if (existingFilter == null)
                        {
                            await _firebaseService.CreateFilterAsync(filter);
                            migratedCount++;
                            _logger.LogInformation("Migrated filter: {Name}", filter.Name);
                        }
                        else
                        {
                            _logger.LogInformation("Filter already exists: {Name}", filter.Name);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error migrating filter: {Name}", filter.Name);
                    }
                }

                _logger.LogInformation("Email filters migration completed. Migrated: {Count}/{Total}", 
                    migratedCount, filters.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating email filters");
                return false;
            }
        }

        public async Task<bool> MigrateProcessedEmailsAsync()
        {
            try
            {
                var emailsFile = "data/processed_emails.json";
                if (!File.Exists(emailsFile))
                {
                    _logger.LogInformation("No processed_emails.json file found, skipping processed emails migration");
                    return true;
                }

                _logger.LogInformation("Migrating processed emails...");
                var json = await File.ReadAllTextAsync(emailsFile);
                var emails = JsonSerializer.Deserialize<List<ProcessedEmail>>(json, _jsonOptions);

                if (emails == null || !emails.Any())
                {
                    _logger.LogInformation("No processed emails to migrate");
                    return true;
                }

                // Migrate in batches to avoid overwhelming Firebase
                const int batchSize = 100;
                var batches = emails.Chunk(batchSize).ToList();
                var totalMigrated = 0;

                for (int i = 0; i < batches.Count; i++)
                {
                    var batch = batches[i];
                    try
                    {
                        // Filter out emails that already exist
                        var newEmails = new List<ProcessedEmail>();
                        foreach (var email in batch)
                        {
                            var exists = await _firebaseService.EmailExistsAsync(email.GmailId);
                            if (!exists)
                            {
                                newEmails.Add(email);
                            }
                        }

                        if (newEmails.Any())
                        {
                            await _firebaseService.CreateProcessedEmailsBatchAsync(newEmails);
                            totalMigrated += newEmails.Count;
                            _logger.LogInformation("Migrated batch {BatchNumber}/{TotalBatches}: {Count} emails", 
                                i + 1, batches.Count, newEmails.Count);
                        }
                        else
                        {
                            _logger.LogInformation("Batch {BatchNumber}/{TotalBatches}: All emails already exist", 
                                i + 1, batches.Count);
                        }

                        // Add delay between batches to avoid rate limiting
                        if (i < batches.Count - 1)
                        {
                            await Task.Delay(1000);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error migrating email batch {BatchNumber}", i + 1);
                    }
                }

                _logger.LogInformation("Processed emails migration completed. Migrated: {Count}/{Total}", 
                    totalMigrated, emails.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating processed emails");
                return false;
            }
        }

        public async Task<bool> MigrateAutoScanSettingsAsync()
        {
            try
            {
                var settingsFile = "data/autoscan_settings.json";
                if (!File.Exists(settingsFile))
                {
                    _logger.LogInformation("No autoscan_settings.json file found, skipping auto scan settings migration");
                    return true;
                }

                _logger.LogInformation("Migrating auto scan settings...");
                var json = await File.ReadAllTextAsync(settingsFile);
                var settings = JsonSerializer.Deserialize<AutoScanSettings>(json, _jsonOptions);

                if (settings == null)
                {
                    _logger.LogInformation("No auto scan settings to migrate");
                    return true;
                }

                try
                {
                    // Check if settings already exist
                    var existingSettings = await _firebaseService.GetAutoScanSettingsAsync();
                    if (existingSettings == null)
                    {
                        await _firebaseService.SaveAutoScanSettingsAsync(settings);
                        _logger.LogInformation("Migrated auto scan settings");
                    }
                    else
                    {
                        _logger.LogInformation("Auto scan settings already exist");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error migrating auto scan settings");
                }

                _logger.LogInformation("Auto scan settings migration completed");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error migrating auto scan settings");
                return false;
            }
        }

        public async Task<bool> BackupDataToJsonAsync()
        {
            try
            {
                _logger.LogInformation("Creating backup of Firebase data to JSON files...");

                var backupDir = $"backup_{DateTime.Now:yyyyMMdd_HHmmss}";
                Directory.CreateDirectory(backupDir);

                // Backup Gmail accounts
                var accounts = await _firebaseService.GetAllAccountsAsync();
                var accountsJson = JsonSerializer.Serialize(accounts, _jsonOptions);
                await File.WriteAllTextAsync(Path.Combine(backupDir, "accounts.json"), accountsJson);

                // Backup email filters
                var filters = await _firebaseService.GetAllFiltersAsync();
                var filtersJson = JsonSerializer.Serialize(filters, _jsonOptions);
                await File.WriteAllTextAsync(Path.Combine(backupDir, "filters.json"), filtersJson);

                // Backup processed emails
                var emails = await _firebaseService.GetAllProcessedEmailsAsync();
                var emailsJson = JsonSerializer.Serialize(emails, _jsonOptions);
                await File.WriteAllTextAsync(Path.Combine(backupDir, "processed_emails.json"), emailsJson);

                // Backup users
                var users = await _firebaseService.GetAllUsersAsync();
                var usersJson = JsonSerializer.Serialize(users, _jsonOptions);
                await File.WriteAllTextAsync(Path.Combine(backupDir, "users.json"), usersJson);

                // Backup auto scan settings
                var autoScanSettings = await _firebaseService.GetAutoScanSettingsAsync();
                if (autoScanSettings != null)
                {
                    var settingsJson = JsonSerializer.Serialize(autoScanSettings, _jsonOptions);
                    await File.WriteAllTextAsync(Path.Combine(backupDir, "autoscan_settings.json"), settingsJson);
                }

                _logger.LogInformation("Backup completed successfully to directory: {BackupDir}", backupDir);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating backup");
                return false;
            }
        }
    }
}
