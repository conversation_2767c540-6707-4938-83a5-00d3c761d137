﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.DTOs
{
    public class ProcessedEmailResponse
    {
        public string Id { get; set; } = string.Empty;
        public string GmailId { get; set; } = string.Empty;
        public string AccountEmail { get; set; } = string.Empty;
        public string Subject { get; set; } = string.Empty;
        public string FromEmail { get; set; } = string.Empty;
        public decimal Amount { get; set; }
        public string Currency { get; set; } = "USD";
        public DateTime EmailDate { get; set; }
        public DateTime ProcessedAt { get; set; }
        public string FilterId { get; set; } = string.Empty;
        public string FilterName { get; set; } = string.Empty;

        public string FormattedAmount => $"{Amount:N2} {Currency}";
        public string RelativeTime => CalculateRelativeTime(EmailDate);

        private static string CalculateRelativeTime(DateTime date)
        {
            var timeSpan = DateTime.UtcNow - date;
            return timeSpan.TotalDays switch
            {
                >= 365 => $"{(int)(timeSpan.TotalDays / 365)} năm trước",
                >= 30 => $"{(int)(timeSpan.TotalDays / 30)} tháng trước",
                >= 7 => $"{(int)(timeSpan.TotalDays / 7)} tuần trước",
                >= 1 => $"{(int)timeSpan.TotalDays} ngày trước",
                _ => timeSpan.TotalHours >= 1 ? $"{(int)timeSpan.TotalHours} giờ trước" : "Vừa xong"
            };
        }
    }
}