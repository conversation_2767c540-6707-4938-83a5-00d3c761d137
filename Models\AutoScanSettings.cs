﻿namespace LSB.SellerMailTracker.API.Models
{
    public class AutoScanSettings
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public bool Enabled { get; set; }

        // ✅ DEPRECATED - Keep for backward compatibility but don't use
        [Obsolete("Use SpecificTimes instead. This field is kept for database compatibility.")]
        public int Interval { get; set; } = 60;

        // ✅ NEW - Support specific times scheduling
        public List<string> SpecificTimes { get; set; } = new() { "09:00", "15:00", "18:00" };

        // ✅ NEW - Schedule type for future extensibility
        public string ScheduleType { get; set; } = "specific_times"; // "interval" or "specific_times"

        public List<string> Accounts { get; set; } = new();
        public List<string> Filters { get; set; } = new();
        public List<string> TemplateFilters { get; set; } = new();
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }

        // ✅ NEW - Track daily scan count for specific times mode
        public int TodayScans { get; set; } = 0;
        public string? LastScanDate { get; set; } // Format: "2025-01-31"

        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime UpdatedAt { get; set; } = DateTime.UtcNow;

        // ✅ Helper Methods
        public DateTime? CalculateNextRunTime()
        {
            if (!Enabled || SpecificTimes == null || !SpecificTimes.Any())
                return null;

            var now = DateTime.UtcNow;
            var today = now.Date;
            var todayString = today.ToString("yyyy-MM-dd");

            // Reset daily scan count if it's a new day
            if (LastScanDate != todayString)
            {
                TodayScans = 0;
                LastScanDate = todayString;
            }

            // Find next scheduled time today
            foreach (var timeStr in SpecificTimes.OrderBy(t => t))
            {
                if (TimeSpan.TryParse(timeStr, out var timeSpan))
                {
                    var nextTime = today.Add(timeSpan);
                    if (nextTime > now)
                    {
                        return nextTime;
                    }
                }
            }

            // No more times today, schedule for tomorrow's first time
            var firstTime = SpecificTimes.OrderBy(t => t).First();
            if (TimeSpan.TryParse(firstTime, out var firstTimeSpan))
            {
                return today.AddDays(1).Add(firstTimeSpan);
            }

            return null;
        }

        public bool ShouldRunNow()
        {
            if (!Enabled || NextRun == null)
                return false;

            return DateTime.UtcNow >= NextRun.Value;
        }

        public void UpdateAfterRun(bool success)
        {
            LastRun = DateTime.UtcNow;

            if (success)
            {
                var today = DateTime.UtcNow.Date.ToString("yyyy-MM-dd");
                if (LastScanDate != today)
                {
                    TodayScans = 1;
                    LastScanDate = today;
                }
                else
                {
                    TodayScans++;
                }
            }

            // Calculate next run time
            NextRun = CalculateNextRunTime();
            UpdatedAt = DateTime.UtcNow;
        }
    }

    public class AutoScanLog
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool Success { get; set; }
        public int AccountsConfigured { get; set; }
        public int AccountsProcessed { get; set; }
        public int EmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ErrorMessage { get; set; }

        // ✅ Enhanced metadata
        public Dictionary<string, object> Metadata { get; set; } = new();

        // ✅ NEW - Scan details
        public string TriggerType { get; set; } = "auto"; // "auto", "manual", "api"
        public List<string>? UsedFilters { get; set; } = new();
        public List<string>? UsedTemplateFilters { get; set; } = new();
        public Dictionary<string, int>? EmailsByAccount { get; set; } = new(); // AccountId -> EmailCount
        public Dictionary<string, decimal>? AmountsByAccount { get; set; } = new(); // AccountId -> Amount

        // ✅ Helper properties
        public TimeSpan Duration => EndTime?.Subtract(StartTime) ?? TimeSpan.Zero;
        public string DurationFormatted => Duration.TotalMinutes > 60
            ? $"{Duration.Hours}h {Duration.Minutes}m {Duration.Seconds}s"
            : Duration.TotalSeconds > 60
            ? $"{Duration.Minutes}m {Duration.Seconds}s"
            : $"{Duration.TotalSeconds:F1}s";

        public string StatusText => Success ? "✅ Success" : "❌ Failed";

        // ✅ Helper methods
        public void MarkCompleted(bool success, string? errorMessage = null)
        {
            EndTime = DateTime.UtcNow;
            Success = success;
            ErrorMessage = errorMessage;
        }

        public void AddAccountResult(string accountId, int emailCount, decimal amount)
        {
            EmailsByAccount ??= new();
            AmountsByAccount ??= new();

            EmailsByAccount[accountId] = emailCount;
            AmountsByAccount[accountId] = amount;
        }

        public void SetFilters(List<string> filters, List<string> templateFilters)
        {
            UsedFilters = filters ?? new();
            UsedTemplateFilters = templateFilters ?? new();
        }
    }

    // ✅ NEW - Enhanced DTOs for better API responses
    public class AutoScanSummary
    {
        public string Id { get; set; } = "";
        public bool IsEnabled { get; set; }
        public string CurrentStatus { get; set; } = "stopped"; // "running", "stopped", "error"
        public List<string> SpecificTimes { get; set; } = new();
        public int AccountCount { get; set; }
        public int FilterCount { get; set; }
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
        public int TodayScans { get; set; }
        public TimeSpan? TimeUntilNextRun => NextRun?.Subtract(DateTime.UtcNow);
        public string? TimeUntilNextRunFormatted => TimeUntilNextRun?.TotalMinutes > 60
            ? $"{TimeUntilNextRun?.Hours}h {TimeUntilNextRun?.Minutes}m"
            : TimeUntilNextRun?.TotalMinutes > 0
            ? $"{TimeUntilNextRun?.Minutes}m {TimeUntilNextRun?.Seconds}s"
            : TimeUntilNextRun?.TotalSeconds > 0
            ? $"{TimeUntilNextRun?.TotalSeconds:F0}s"
            : "Ready to run";
    }
}