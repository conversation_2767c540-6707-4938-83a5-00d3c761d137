{"info": {"name": "JWT Authentication API", "description": "Collection for testing JWT authentication endpoints", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "https://localhost:7126", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "refreshToken", "value": "", "type": "string"}], "item": [{"name": "Authentication", "item": [{"name": "Register", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"test123\",\n  \"confirmPassword\": \"test123\",\n  \"name\": \"Test User\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/register", "host": ["{{baseUrl}}"], "path": ["api", "auth", "register"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('token', response.data.token);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "    }", "}"]}}]}, {"name": "<PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"admin123\",\n  \"rememberMe\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('token', response.data.token);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "    }", "}"]}}]}, {"name": "Login Test User", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\",\n  \"password\": \"test123\",\n  \"rememberMe\": false\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/login", "host": ["{{baseUrl}}"], "path": ["api", "auth", "login"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('token', response.data.token);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "    }", "}"]}}]}, {"name": "Refresh <PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"refreshToken\": \"{{refreshToken}}\"\n}"}, "url": {"raw": "{{baseUrl}}/api/auth/refresh-jwt-token", "host": ["{{baseUrl}}"], "path": ["api", "auth", "refresh-jwt-token"]}}, "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    if (response.success && response.data) {", "        pm.collectionVariables.set('token', response.data.token);", "        pm.collectionVariables.set('refreshToken', response.data.refreshToken);", "    }", "}"]}}]}, {"name": "Logout", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/auth/logout", "host": ["{{baseUrl}}"], "path": ["api", "auth", "logout"]}}}]}, {"name": "Protected Endpoints", "item": [{"name": "Test JWT", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/user/test", "host": ["{{baseUrl}}"], "path": ["api", "user", "test"]}}}, {"name": "Get Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/user/profile", "host": ["{{baseUrl}}"], "path": ["api", "user", "profile"]}}}, {"name": "Admin Test", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/api/user/admin-test", "host": ["{{baseUrl}}"], "path": ["api", "user", "admin-test"]}}}]}]}