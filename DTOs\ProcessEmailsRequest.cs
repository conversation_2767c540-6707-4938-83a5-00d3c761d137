﻿using System.ComponentModel.DataAnnotations;

namespace LSB.SellerMailTracker.API.DTOs
{
    public class ProcessEmailsRequest
    {
        public List<string> AccountIds { get; set; } = new();
        public List<string> FilterIds { get; set; } = new();

        public DateTime? FromDate { get; set; }
        public DateTime? ToDate { get; set; }

        [Range(1, 1000, ErrorMessage = "MaxResults phải từ 1 đến 1000")]
        public int MaxResults { get; set; } = 100;
    }
}
