﻿using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Imple;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IAutoScanService
    {
        #region Settings Management
        /// <summary>
        /// Get current auto-scan settings
        /// </summary>
        Task<AutoScanSettings?> GetAutoScanSettingsAsync();

        /// <summary>
        /// Save auto-scan settings
        /// </summary>
        Task SaveAutoScanSettingsAsync(AutoScanSettings settings);

        /// <summary>
        /// Delete auto-scan settings
        /// </summary>
        Task DeleteAutoScanSettingsAsync();
        #endregion

        #region Logs Management
        /// <summary>
        /// Save auto-scan log entry
        /// </summary>
        Task SaveAutoScanLogAsync(AutoScanLog log);

        /// <summary>
        /// Get auto-scan logs with limit
        /// </summary>
        Task<List<AutoScanLog>> GetAutoScanLogsAsync(int limit = 20);

        /// <summary>
        /// Get specific auto-scan log by ID
        /// </summary>
        Task<AutoScanLog?> GetAutoScanLogAsync(string logId);

        /// <summary>
        /// Delete auto-scan logs older than specified date
        /// </summary>
        Task DeleteAutoScanLogsAsync(DateTime olderThan);
        #endregion

        #region Auto-Scan Execution
        /// <summary>
        /// Execute auto-scan with given settings
        /// </summary>
        Task<AutoScanResult> ExecuteAutoScanAsync(AutoScanSettings settings);
        #endregion

        #region Cleanup and Maintenance
        /// <summary>
        /// Cleanup old logs based on retention days
        /// </summary>
        Task<int> CleanupOldLogsAsync(int retentionDays = 30);

        /// <summary>
        /// Get auto-scan statistics and metrics
        /// </summary>
        Task<AutoScanStatistics> GetAutoScanStatisticsAsync();
        #endregion
    }
}