﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IDataService
    {
        // Account Management
        Task SaveAccountAsync(GmailAccount account);
        Task<GmailAccount?> GetAccountAsync(string accountId);
        Task<List<GmailAccount>> GetAllAccountsAsync();
        Task UpdateAccountAsync(GmailAccount account);
        Task DeleteAccountAsync(string accountId);
        Task<List<GmailAccount>> GetActiveAccountsAsync();

        // Filter Management
        Task SaveFilterAsync(EmailFilter filter);
        Task<EmailFilter?> GetFilterAsync(string filterId);
        Task<List<EmailFilter>> GetAllFiltersAsync();
        Task UpdateFilterAsync(EmailFilter filter);
        Task DeleteFilterAsync(string filterId);
        Task<List<EmailFilter>> GetActiveFiltersAsync();

        // Email Management
        Task SaveProcessedEmailAsync(ProcessedEmail email);
        Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId);
        Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId);
        Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync();
        Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail);
        Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId);
        Task<List<ProcessedEmail>> GetProcessedEmailsByAccountIdAsync(string accountId);
        Task<List<ProcessedEmail>> GetRecentProcessedEmailsAsync(int days = 30);
        Task SaveProcessedEmailsBatchAsync(List<ProcessedEmail> emails);
        Task<bool> EmailExistsAsync(string gmailId);
        Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds);
        Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page,
            int pageSize,
            string? accountEmail = null,
            string? filterId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null);

        // Statistics
        Task<DashboardStats> GetDashboardStatsAsync();
        Task<Dictionary<string, CurrencyStats>> GetCurrencyStatsAsync(int days = 30);
        Task<ProcessingStatsResult> GetProcessingStatsAsync(DateTime? fromDate = null, DateTime? toDate = null);
    }
}