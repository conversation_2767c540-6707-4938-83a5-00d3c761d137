using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    [Authorize] // Require JWT authentication for all endpoints
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;
        private readonly ILogger<UserController> _logger;

        public UserController(IUserService userService, ILogger<UserController> logger)
        {
            _userService = userService;
            _logger = logger;
        }

        /// <summary>
        /// Get current user profile (protected endpoint)
        /// </summary>
        [HttpGet("profile")]
        public async Task<IActionResult> GetProfile()
        {
            try
            {
                var userId = User.FindFirst("userId")?.Value;
                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest(new { error = "User ID not found in token" });
                }

                var user = await _userService.GetUserByIdAsync(userId);
                if (user == null)
                {
                    return NotFound(new { error = "User not found" });
                }

                var userInfo = new UserInfo
                {
                    Id = user.Id,
                    Email = user.Email,
                    Name = user.Name,
                    Role = user.Role,
                    CreatedAt = user.CreatedAt,
                    LastLoginAt = user.LastLoginAt
                };

                return Ok(ApiResponse<UserInfo>.SuccessResult(userInfo, "Profile retrieved successfully"));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user profile");
                return BadRequest(ApiResponse<UserInfo>.ErrorResult("Error retrieving profile"));
            }
        }

        /// <summary>
        /// Test endpoint to verify JWT authentication
        /// </summary>
        [HttpGet("test")]
        public IActionResult Test()
        {
            var claims = User.Claims.Select(c => new { c.Type, c.Value }).ToList();
            
            return Ok(new
            {
                message = "JWT Authentication is working!",
                user = new
                {
                    id = User.FindFirst("userId")?.Value,
                    email = User.FindFirst(ClaimTypes.Email)?.Value,
                    name = User.FindFirst(ClaimTypes.Name)?.Value,
                    role = User.FindFirst(ClaimTypes.Role)?.Value
                },
                claims = claims,
                timestamp = DateTime.UtcNow
            });
        }

        /// <summary>
        /// Admin only endpoint (requires Admin role)
        /// </summary>
        [HttpGet("admin-test")]
        [Authorize(Roles = "Admin")]
        public IActionResult AdminTest()
        {
            return Ok(new
            {
                message = "Admin access granted!",
                user = User.FindFirst(ClaimTypes.Name)?.Value,
                timestamp = DateTime.UtcNow
            });
        }
    }
}
