﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.DTOs
{
    public class EmailFilterResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string BodyContains { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }
}