# Firebase Integration - LSB Seller Mail Tracker API

## 🎉 Tổng quan

Dự án đã được tích hợp Firebase Firestore để thay thế việc lưu trữ dữ liệu trong file JSON. Điều này mang lại nhiều lợi ích:

- ✅ **Scalability**: <PERSON><PERSON> thể mở rộng dễ dàng
- ✅ **Real-time**: <PERSON><PERSON><PERSON> bộ dữ liệu real-time
- ✅ **Security**: <PERSON><PERSON><PERSON> mật tốt hơn với Firebase Security Rules
- ✅ **Backup**: Tự động backup và disaster recovery
- ✅ **Performance**: Truy vấn nhanh hơn với indexing

## 📁 Files đã tạo/cập nhật

### 🆕 Files mới:
- `Models/FirebaseSettings.cs` - Cấu hình Firebase
- `Services/Interfaces/IFirebaseService.cs` - Interface Firebase service
- `Services/Imple/FirebaseService.cs` - Implementation Firebase service
- `Services/Imple/FirebaseUserService.cs` - User service sử dụng Firebase
- `Services/Imple/FirebaseAccountService.cs` - Account service sử dụng Firebase
- `Services/Imple/DataMigrationService.cs` - Service migration dữ liệu
- `Controllers/MigrationController.cs` - API endpoints cho migration
- `FIREBASE_SETUP_GUIDE.md` - Hướng dẫn setup Firebase
- `test-firebase-api.http` - Test cases cho Firebase API

### 🔄 Files đã cập nhật:
- `LSB.SellerMailTracker.API.csproj` - Thêm Firebase packages
- `appsettings.json` - Thêm Firebase configuration
- `appsettings.Development.json` - Thêm Firebase configuration
- `Program.cs` - Đăng ký Firebase services

## 🚀 Cách sử dụng

### Bước 1: Setup Firebase
1. Làm theo hướng dẫn trong `FIREBASE_SETUP_GUIDE.md`
2. Tạo Firebase project và Firestore database
3. Download service account key và đặt vào project
4. Cập nhật `appsettings.json` với thông tin Firebase

### Bước 2: Chạy project
```bash
dotnet run
```

### Bước 3: Test Firebase connection
```bash
# Login as admin
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "admin123"
}

# Check Firebase status
GET /api/migration/status
Authorization: Bearer <admin-token>
```

### Bước 4: Migration dữ liệu (nếu có)
```bash
# Migrate all data from JSON to Firebase
POST /api/migration/migrate-all
Authorization: Bearer <admin-token>
```

## 🔧 Architecture

### Firebase Collections:
- **users** - JWT authentication users
- **gmail_accounts** - OAuth Gmail accounts
- **email_filters** - Email processing filters
- **processed_emails** - Processed email data

### Service Layer:
```
Controllers
    ↓
AuthService/UserService (JWT)
    ↓
FirebaseUserService
    ↓
FirebaseService
    ↓
Firestore Database
```

## 📊 Migration Features

### Automatic Migration:
- Tự động migrate dữ liệu từ JSON files sang Firebase
- Kiểm tra duplicate để tránh import trùng
- Batch processing cho performance tốt
- Detailed logging cho debugging

### Migration Endpoints:
- `POST /api/migration/migrate-all` - Migrate tất cả dữ liệu
- `POST /api/migration/migrate-accounts` - Migrate Gmail accounts
- `POST /api/migration/migrate-filters` - Migrate email filters
- `POST /api/migration/migrate-emails` - Migrate processed emails
- `POST /api/migration/backup` - Backup Firebase data to JSON

## 🔐 Security

### Development:
- Firebase Security Rules ở chế độ test (allow all)
- Service account key trong project folder

### Production:
- Cập nhật Firebase Security Rules chặt chẽ
- Sử dụng environment variables cho sensitive data
- Enable Firebase Authentication nếu cần

## 🧪 Testing

### Test Firebase Integration:
```bash
# Test Firebase operations
POST /api/migration/test
Authorization: Bearer <admin-token>

# Register new user (saves to Firebase)
POST /api/auth/register
{
  "email": "<EMAIL>",
  "password": "test123",
  "confirmPassword": "test123",
  "name": "Test User"
}

# Login user (reads from Firebase)
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "test123"
}
```

### Test Files:
- `test-firebase-api.http` - HTTP test cases
- `JWT_API_Postman_Collection.json` - Postman collection (updated)

## 📈 Performance

### Optimizations:
- **Batch operations** cho bulk insert/update
- **Indexing** tự động của Firestore
- **Caching** có thể thêm sau với Redis
- **Pagination** cho large datasets

### Monitoring:
- Firebase Console để monitor usage
- Application logs cho debugging
- Performance metrics trong Firebase

## 🔄 Backward Compatibility

### Dual Support:
- Vẫn giữ JSON file services để backup
- Có thể switch giữa JSON và Firebase
- Migration tools để chuyển đổi

### Rollback Plan:
- Backup Firebase data to JSON
- Switch service registration trong Program.cs
- Restore từ JSON files nếu cần

## 🛠️ Troubleshooting

### Common Issues:

1. **Firebase connection failed**
   - Kiểm tra service account key path
   - Verify Firebase project ID
   - Check internet connection

2. **Permission denied**
   - Update Firestore Security Rules
   - Verify service account permissions

3. **Migration errors**
   - Check source JSON file format
   - Verify Firebase collections exist
   - Review migration logs

### Debug Commands:
```bash
# Check Firebase status
GET /api/migration/status

# Test Firebase operations
POST /api/migration/test

# View application logs
dotnet run --verbosity detailed
```

## 🎯 Next Steps

### Enhancements:
1. **Real-time updates** với Firebase listeners
2. **Advanced querying** với composite indexes
3. **File storage** với Firebase Storage
4. **Push notifications** với Firebase Cloud Messaging
5. **Analytics** với Firebase Analytics

### Production Readiness:
1. Environment-based configuration
2. Proper error handling và retry logic
3. Rate limiting và throttling
4. Monitoring và alerting
5. Backup strategy

## 📞 Support

Nếu gặp vấn đề:
1. Check logs trong console
2. Review Firebase Console
3. Test với `test-firebase-api.http`
4. Check `FIREBASE_SETUP_GUIDE.md`
