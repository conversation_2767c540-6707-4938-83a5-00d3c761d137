﻿using System.ComponentModel.DataAnnotations;

namespace LSB.SellerMailTracker.API.DTOs
{
    public class UpdateFilterRequest
    {
        [Required]
        [StringLength(100, ErrorMessage = "Tên bộ lọc không được quá 100 ký tự")]
        public string Name { get; set; } = string.Empty;

        [Required]
        [StringLength(500, ErrorMessage = "Nội dung cần lọc không được quá 500 ký tự")]
        public string BodyContains { get; set; } = string.Empty;

        [StringLength(500, ErrorMessage = "Mô tả không được quá 500 ký tự")]
        public string Description { get; set; } = string.Empty;

        public bool IsActive { get; set; } = true;
    }
}