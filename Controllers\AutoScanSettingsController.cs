﻿using Microsoft.AspNetCore.Mvc;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class AutoScanSettingsController : ControllerBase
    {
        private readonly IAutoScanService _autoScanService;
        private readonly IDataService _dataService;
        private readonly ILogger<AutoScanSettingsController> _logger;

        public AutoScanSettingsController(
            IAutoScanService autoScanService,
            IDataService dataService,
            ILogger<AutoScanSettingsController> logger)
        {
            _autoScanService = autoScanService;
            _dataService = dataService;
            _logger = logger;
        }

        [HttpGet("settings")]
        public async Task<ActionResult<AutoScanSettingsResponse>> GetAutoScanSettings()
        {
            try
            {
                _logger.LogInformation("🔍 Getting auto-scan settings");
                var settings = await _autoScanService.GetAutoScanSettingsAsync();

                if (settings == null)
                {
                    _logger.LogInformation("📋 No settings found, returning defaults");
                    var defaultSettings = new AutoScanSettingsResponse
                    {
                        Enabled = false,
                        SpecificTimes = new List<string> { "09:00", "15:00", "18:00" }, // ✅ Default times
                        Accounts = new List<string>(),
                        Filters = new List<string>(),
                        TemplateFilters = new List<string>(),
                        LastRun = null,
                        NextRun = null
                    };

                    return Ok(defaultSettings);
                }

                var response = new AutoScanSettingsResponse
                {
                    Enabled = settings.Enabled,
                    SpecificTimes = settings.SpecificTimes ?? new List<string> { "09:00", "15:00", "18:00" },
                    Accounts = settings.Accounts,
                    Filters = settings.Filters,
                    TemplateFilters = settings.TemplateFilters,
                    LastRun = settings.LastRun,
                    NextRun = settings.NextRun
                };

                _logger.LogInformation($"✅ Retrieved settings: Enabled={response.Enabled}, Times={response.SpecificTimes.Count}, Accounts={response.Accounts.Count}, Filters={response.Filters.Count + response.TemplateFilters.Count}");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error retrieving auto-scan settings");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost("settings")]
        public async Task<ActionResult<AutoScanSettingsResponse>> SaveAutoScanSettings(
            [FromBody] AutoScanSettingsRequest request)
        {
            try
            {
                _logger.LogInformation($"💾 Saving auto-scan settings: Enabled={request.Enabled}, Times={request.SpecificTimes?.Count ?? 0}, Accounts={request.Accounts?.Count ?? 0}");

                var validationError = await ValidateAutoScanRequest(request);
                if (!string.IsNullOrEmpty(validationError))
                {
                    return BadRequest(validationError);
                }

                var settings = new AutoScanSettings
                {
                    Enabled = request.Enabled,
                    SpecificTimes = request.SpecificTimes ?? new List<string> { "09:00", "15:00", "18:00" },
                    Accounts = request.Accounts ?? new List<string>(),
                    Filters = request.Filters ?? new List<string>(),
                    TemplateFilters = request.TemplateFilters ?? new List<string>(),
                    LastRun = request.LastRun,
                    NextRun = request.NextRun, // ✅ Giữ nguyên NextRun từ frontend
                    UpdatedAt = DateTime.UtcNow
                };

                // ✅ KHÔNG tự động set NextRun - để frontend quản lý
                // Frontend sẽ tính toán và gửi đúng NextRun

                await _autoScanService.SaveAutoScanSettingsAsync(settings);

                var response = new AutoScanSettingsResponse
                {
                    Enabled = settings.Enabled,
                    SpecificTimes = settings.SpecificTimes,
                    Accounts = settings.Accounts,
                    Filters = settings.Filters,
                    TemplateFilters = settings.TemplateFilters,
                    LastRun = settings.LastRun,
                    NextRun = settings.NextRun
                };

                _logger.LogInformation($"✅ Auto-scan settings saved successfully. Next run: {settings.NextRun}");
                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error saving auto-scan settings");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost("toggle")]
        public async Task<ActionResult<AutoScanSettingsResponse>> ToggleAutoScan()
        {
            try
            {
                _logger.LogInformation("🔄 Toggling auto-scan status");
                var currentSettings = await _autoScanService.GetAutoScanSettingsAsync();

                if (currentSettings == null)
                {
                    _logger.LogWarning("⚠️ No auto-scan settings found for toggle");
                    return BadRequest("No auto-scan settings found. Please configure settings first.");
                }

                currentSettings.Enabled = !currentSettings.Enabled;
                currentSettings.UpdatedAt = DateTime.UtcNow;

                if (currentSettings.Enabled)
                {
                    // ✅ Tính toán NextRun dựa trên SpecificTimes
                    currentSettings.NextRun = CalculateNextRunTime(currentSettings.SpecificTimes);
                    _logger.LogInformation($"✅ Auto-scan ENABLED. Next run: {currentSettings.NextRun}");
                }
                else
                {
                    currentSettings.NextRun = null;
                    _logger.LogInformation("🛑 Auto-scan DISABLED");
                }

                await _autoScanService.SaveAutoScanSettingsAsync(currentSettings);

                var response = new AutoScanSettingsResponse
                {
                    Enabled = currentSettings.Enabled,
                    SpecificTimes = currentSettings.SpecificTimes ?? new List<string> { "09:00", "15:00", "18:00" },
                    Accounts = currentSettings.Accounts,
                    Filters = currentSettings.Filters,
                    TemplateFilters = currentSettings.TemplateFilters,
                    LastRun = currentSettings.LastRun,
                    NextRun = currentSettings.NextRun
                };

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error toggling auto-scan");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpPost("run-now")]
        public async Task<ActionResult<AutoScanRunResponse>> RunAutoScanNow()
        {
            try
            {
                var settings = await _autoScanService.GetAutoScanSettingsAsync();

                if (settings == null || settings.Accounts?.Count == 0)
                {
                    _logger.LogWarning("⚠️ No auto-scan settings or accounts configured");
                    return BadRequest("No auto-scan settings or accounts configured");
                }

                _logger.LogInformation($"🚀 Manual auto-scan started with {settings.Accounts.Count} accounts and {settings.Filters.Count + settings.TemplateFilters.Count} filters");

                var result = await _autoScanService.ExecuteAutoScanAsync(settings);

                // ✅ Cập nhật LastRun sau khi chạy thành công
                if (result.Success)
                {
                    settings.LastRun = result.EndTime;
                    await _autoScanService.SaveAutoScanSettingsAsync(settings);
                }

                var response = new AutoScanRunResponse
                {
                    Success = result.Success,
                    StartTime = result.StartTime,
                    EndTime = result.EndTime,
                    AccountsProcessed = result.AccountsProcessed,
                    EmailsProcessed = result.EmailsProcessed,
                    TotalAmount = result.TotalAmount,
                    ErrorMessage = result.ErrorMessage
                };

                _logger.LogInformation($"🎉 Manual auto-scan completed: Success={result.Success}, Accounts={result.AccountsProcessed}, Emails={result.EmailsProcessed}, Amount=${result.TotalAmount:F2}");

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error running manual auto-scan");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("status")]
        public async Task<ActionResult<AutoScanStatusResponse>> GetAutoScanStatus()
        {
            try
            {
                var settings = await _autoScanService.GetAutoScanSettingsAsync();
                var logs = await _autoScanService.GetAutoScanLogsAsync(10);

                var status = new AutoScanStatusResponse
                {
                    IsEnabled = settings?.Enabled ?? false,
                    CurrentStatus = settings?.Enabled == true ? "running" : "stopped",
                    SpecificTimes = settings?.SpecificTimes ?? new List<string> { "09:00", "15:00", "18:00" },
                    AccountCount = settings?.Accounts?.Count ?? 0,
                    FilterCount = (settings?.Filters?.Count ?? 0) + (settings?.TemplateFilters?.Count ?? 0),
                    LastRun = settings?.LastRun,
                    NextRun = settings?.NextRun,
                    RecentLogs = logs?.Select(log => new AutoScanLogResponse
                    {
                        Id = log.Id,
                        StartTime = log.StartTime,
                        EndTime = log.EndTime,
                        Success = log.Success,
                        AccountsProcessed = log.AccountsProcessed,
                        EmailsProcessed = log.EmailsProcessed,
                        TotalAmount = log.TotalAmount,
                        ErrorMessage = log.ErrorMessage,
                        Duration = log.EndTime.HasValue ? log.EndTime.Value - log.StartTime : TimeSpan.Zero
                    }).ToList() ?? new List<AutoScanLogResponse>()
                };

                return Ok(status);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error retrieving auto-scan status");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        [HttpGet("logs")]
        public async Task<ActionResult<List<AutoScanLogResponse>>> GetAutoScanLogs([FromQuery] int limit = 20)
        {
            try
            {
                var logs = await _autoScanService.GetAutoScanLogsAsync(limit);

                var response = logs.Select(log => new AutoScanLogResponse
                {
                    Id = log.Id,
                    StartTime = log.StartTime,
                    EndTime = log.EndTime,
                    Success = log.Success,
                    AccountsProcessed = log.AccountsProcessed,
                    EmailsProcessed = log.EmailsProcessed,
                    TotalAmount = log.TotalAmount,
                    ErrorMessage = log.ErrorMessage,
                    Duration = log.EndTime.HasValue ? log.EndTime.Value - log.StartTime : TimeSpan.Zero
                }).ToList();

                return Ok(response);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error retrieving auto-scan logs");
                return StatusCode(500, new { error = ex.Message });
            }
        }

        // ✅ Helper method để tính NextRun từ SpecificTimes
        private DateTime? CalculateNextRunTime(List<string> specificTimes)
        {
            if (specificTimes == null || !specificTimes.Any())
                return null;

            var now = DateTime.UtcNow;
            var today = now.Date;

            // Tìm thời gian tiếp theo trong ngày
            foreach (var timeStr in specificTimes.OrderBy(t => t))
            {
                if (TimeSpan.TryParse(timeStr, out var timeSpan))
                {
                    var nextTime = today.Add(timeSpan);
                    if (nextTime > now)
                    {
                        return nextTime;
                    }
                }
            }

            // Nếu không có thời gian nào trong ngày, lấy thời gian đầu tiên của ngày mai
            var firstTime = specificTimes.OrderBy(t => t).First();
            if (TimeSpan.TryParse(firstTime, out var firstTimeSpan))
            {
                return today.AddDays(1).Add(firstTimeSpan);
            }

            return null;
        }

        private async Task<string?> ValidateAutoScanRequest(AutoScanSettingsRequest request)
        {
            // ✅ Validate SpecificTimes instead of Interval
            if (request.SpecificTimes?.Any() == true)
            {
                foreach (var timeStr in request.SpecificTimes)
                {
                    if (!TimeSpan.TryParse(timeStr, out var timeSpan) ||
                        timeSpan < TimeSpan.Zero ||
                        timeSpan >= TimeSpan.FromDays(1))
                    {
                        return $"Invalid time format: {timeStr}. Use HH:mm format (00:00 - 23:59)";
                    }
                }
            }

            if (request.Accounts?.Any() == true)
            {
                var allAccounts = await _dataService.GetAllAccountsAsync();
                var validAccountIds = allAccounts.Where(a => a.IsActive).Select(a => a.Id).ToHashSet();
                var invalidAccounts = request.Accounts.Where(id => !validAccountIds.Contains(id)).ToList();

                if (invalidAccounts.Any())
                {
                    _logger.LogWarning($"⚠️ Invalid account IDs: {string.Join(", ", invalidAccounts)}");
                    return $"Invalid account IDs: {string.Join(", ", invalidAccounts)}";
                }
            }

            if (request.Filters?.Any() == true)
            {
                var allFilters = await _dataService.GetAllFiltersAsync();
                var validFilterIds = allFilters.Where(f => f.IsActive).Select(f => f.Id).ToHashSet();
                var invalidFilters = request.Filters.Where(id => !validFilterIds.Contains(id)).ToList();

                if (invalidFilters.Any())
                {
                    _logger.LogWarning($"⚠️ Invalid filter IDs: {string.Join(", ", invalidFilters)}");
                    return $"Invalid filter IDs: {string.Join(", ", invalidFilters)}";
                }
            }

            if (request.Enabled && request.NextRun.HasValue && request.NextRun.Value <= DateTime.UtcNow)
            {
                return "Next run time must be in the future when auto-scan is enabled";
            }

            return null;
        }
    }

    // ✅ Updated DTOs
    public class AutoScanSettingsRequest
    {
        public bool Enabled { get; set; }
        public List<string> SpecificTimes { get; set; } = new(); // ✅ Changed from Interval
        public List<string> Accounts { get; set; } = new();
        public List<string> Filters { get; set; } = new();
        public List<string> TemplateFilters { get; set; } = new();
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
    }

    public class AutoScanSettingsResponse
    {
        public bool Enabled { get; set; }
        public List<string> SpecificTimes { get; set; } = new(); // ✅ Changed from Interval
        public List<string> Accounts { get; set; } = new();
        public List<string> Filters { get; set; } = new();
        public List<string> TemplateFilters { get; set; } = new();
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
    }

    public class AutoScanStatusResponse
    {
        public bool IsEnabled { get; set; }
        public string CurrentStatus { get; set; } = "stopped";
        public List<string> SpecificTimes { get; set; } = new(); // ✅ Changed from Interval
        public int AccountCount { get; set; }
        public int FilterCount { get; set; }
        public DateTime? LastRun { get; set; }
        public DateTime? NextRun { get; set; }
        public List<AutoScanLogResponse> RecentLogs { get; set; } = new();
    }

    public class AutoScanRunResponse
    {
        public bool Success { get; set; }
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public int AccountsProcessed { get; set; }
        public int EmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration => EndTime - StartTime;
    }

    public class AutoScanLogResponse
    {
        public string Id { get; set; } = "";
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public bool Success { get; set; }
        public int AccountsProcessed { get; set; }
        public int EmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ErrorMessage { get; set; }
        public TimeSpan Duration { get; set; }
    }
}