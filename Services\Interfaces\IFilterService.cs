﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IFilterService
    {
        Task SaveFilterAsync(EmailFilter filter);
        Task<EmailFilter?> GetFilterAsync(string filterId);
        Task<List<EmailFilter>> GetAllFiltersAsync();
        Task<List<EmailFilter>> GetActiveFiltersAsync();
        Task UpdateFilterAsync(EmailFilter filter);
        Task DeleteFilterAsync(string filterId);
    }
}