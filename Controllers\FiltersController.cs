﻿using System.Text.Json;
using LSB.SellerMailTracker.API.Controllers;
using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using Microsoft.AspNetCore.Mvc;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class FiltersController : ControllerBase
    {
        private readonly IDataService _dataService;
        private readonly IEmailFilterService _filterService;
        private readonly ITemplateService _templateService;

        public FiltersController(
            IDataService dataService,
            IEmailFilterService filterService,
            ITemplateService templateService)
        {
            _dataService = dataService;
            _filterService = filterService;
            _templateService = templateService;
        }

        [HttpPost]
        public async Task<IActionResult> CreateFilter([FromBody] CreateFilterRequest request)
        {
            try
            {
                // Validate request
                var validationResult = ValidateFilterRequest(request);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { error = validationResult.ErrorMessage });
                }

                var filter = await CreateFilterFromRequest(request);
                await _dataService.SaveFilterAsync(filter);

                var response = filter.ToFilterResponse();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet]
        public async Task<IActionResult> GetFilters()
        {
            try
            {
                var filters = await _dataService.GetAllFiltersAsync();
                var response = filters.Select(f => f.ToFilterResponse()).ToList();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("{filterId}")]
        public async Task<IActionResult> GetFilter(string filterId)
        {
            try
            {
                var filter = await _dataService.GetFilterAsync(filterId);
                if (filter == null)
                    return NotFound(new { error = "Filter not found" });

                var response = filter.ToFilterResponse();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPut("{filterId}")]
        public async Task<IActionResult> UpdateFilter(string filterId, [FromBody] UpdateFilterRequest request)
        {
            try
            {
                var filter = await _dataService.GetFilterAsync(filterId);
                if (filter == null)
                    return NotFound(new { error = "Filter not found" });

                // Validate request
                var validationResult = ValidateFilterRequest(request);
                if (!validationResult.IsValid)
                {
                    return BadRequest(new { error = validationResult.ErrorMessage });
                }

                // Update filter properties
                await UpdateFilterFromRequest(filter, request);
                await _dataService.UpdateFilterAsync(filter);

                var response = filter.ToFilterResponse();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpDelete("{filterId}")]
        public async Task<IActionResult> DeleteFilter(string filterId)
        {
            try
            {
                var filter = await _dataService.GetFilterAsync(filterId);
                if (filter == null)
                    return NotFound(new { error = "Filter not found" });

                await _dataService.DeleteFilterAsync(filterId);
                return Ok(new { message = "Filter deleted successfully" });
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("templates")]
        public async Task<IActionResult> GetTemplates()
        {
            try
            {
                var templates = _templateService.GetEmailTemplates();
                return Ok(templates);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpGet("templates/{templateId}")]
        public async Task<IActionResult> GetTemplate(string templateId)
        {
            try
            {
                var template = _templateService.GetEmailTemplate(templateId);
                if (template == null)
                    return NotFound(new { error = "Template not found" });

                return Ok(template);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("quick/income")]
        public async Task<IActionResult> CreateIncomeFilter()
        {
            try
            {
                var filter = await _templateService.CreateSmartIncomeFilter();
                await _dataService.SaveFilterAsync(filter);

                var response = filter.ToFilterResponse();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        [HttpPost("quick/outcome")]
        public async Task<IActionResult> CreateOutcomeFilter()
        {
            try
            {
                var filter = await _templateService.CreateSmartOutcomeFilter();
                await _dataService.SaveFilterAsync(filter);

                var response = filter.ToFilterResponse();
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }


        [HttpGet("stats")]
        public async Task<IActionResult> GetFilterStats()
        {
            try
            {
                var filters = await _dataService.GetAllFiltersAsync();
                var stats = new
                {
                    totalFilters = filters.Count,
                    activeFilters = filters.Count(f => f.IsActive),
                    incomeFilters = filters.Count(f => f.Type == "income"),
                    outcomeFilters = filters.Count(f => f.Type == "outcome"),
                    templateFilters = filters.Count(f => f.Type == "template"),
                    customFilters = filters.Count(f => f.Type == "custom"),
                    totalExtractionFields = filters
                        .Where(f => f.ExtractionRules != null)
                        .SelectMany(f => f.ExtractionRules.Keys)
                        .Distinct()
                        .Count(),
                    supportedPlatforms = _templateService.GetSupportedPlatforms()
                };

                return Ok(stats);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }

        #region Private Methods

        private async Task<EmailFilter> CreateFilterFromRequest(CreateFilterRequest request)
        {
            var filter = new EmailFilter
            {
                Id = Guid.NewGuid().ToString(),
                Name = request.Name,
                Description = request.Description,
                Type = request.Type,
                TemplateId = request.TemplateId,
                IsActive = request.IsActive,
                MatchCondition = request.MatchCondition,
                Conditions = request.Conditions?.Select(c => new FilterCondition
                {
                    Id = c.Id,
                    Type = c.Type,
                    Operator = c.Operator,
                    Value = c.Value
                }).ToList() ?? new List<FilterCondition>(),
                GmailFilters = request.GmailFilters != null ? new GmailFilterOptions
                {
                    Labels = request.GmailFilters.Labels,
                    HasAttachment = request.GmailFilters.HasAttachment,
                    IsRead = request.GmailFilters.IsRead,
                    IsImportant = request.GmailFilters.IsImportant,
                    IsStarred = request.GmailFilters.IsStarred,
                    DateRange = request.GmailFilters.DateRange != null ? new DateRangeFilter
                    {
                        Type = request.GmailFilters.DateRange.Type,
                        Value = request.GmailFilters.DateRange.Value,
                        StartDate = request.GmailFilters.DateRange.StartDate,
                        EndDate = request.GmailFilters.DateRange.EndDate
                    } : null
                } : null,
                ExtractionRules = request.ExtractionRules ?? new Dictionary<string, ExtractionRule>(),
                CustomQuery = request.CustomQuery,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            // If it's a template-based filter, apply template rules
            if (!string.IsNullOrEmpty(request.TemplateId))
            {
                await _templateService.ApplyTemplateToFilter(filter, request.TemplateId);
            }

            // If it's a smart income/outcome filter, apply smart rules
            if (request.Type == "income" || request.Type == "outcome")
            {
                await _templateService.ApplySmartRulesToFilter(filter, request.Type);
            }

            return filter;
        }

        private async Task UpdateFilterFromRequest(EmailFilter filter, UpdateFilterRequest request)
        {
            filter.Name = request.Name;
            filter.Description = request.Description;
            filter.Type = request.Type;
            filter.TemplateId = request.TemplateId;
            filter.IsActive = request.IsActive;
            filter.MatchCondition = request.MatchCondition;
            filter.UpdatedAt = DateTime.UtcNow;

            if (request.Conditions != null)
            {
                filter.Conditions = request.Conditions.Select(c => new FilterCondition
                {
                    Id = c.Id,
                    Type = c.Type,
                    Operator = c.Operator,
                    Value = c.Value
                }).ToList();
            }

            if (request.GmailFilters != null)
            {
                filter.GmailFilters = new GmailFilterOptions
                {
                    Labels = request.GmailFilters.Labels,
                    HasAttachment = request.GmailFilters.HasAttachment,
                    IsRead = request.GmailFilters.IsRead,
                    IsImportant = request.GmailFilters.IsImportant,
                    IsStarred = request.GmailFilters.IsStarred,
                    DateRange = request.GmailFilters.DateRange != null ? new DateRangeFilter
                    {
                        Type = request.GmailFilters.DateRange.Type,
                        Value = request.GmailFilters.DateRange.Value,
                        StartDate = request.GmailFilters.DateRange.StartDate,
                        EndDate = request.GmailFilters.DateRange.EndDate
                    } : null
                };
            }

            if (request.ExtractionRules != null)
            {
                filter.ExtractionRules = request.ExtractionRules;
            }

            filter.CustomQuery = request.CustomQuery;

            // Reapply template/smart rules if needed
            if (!string.IsNullOrEmpty(request.TemplateId))
            {
                await _templateService.ApplyTemplateToFilter(filter, request.TemplateId);
            }
            else if (request.Type == "income" || request.Type == "outcome")
            {
                await _templateService.ApplySmartRulesToFilter(filter, request.Type);
            }
        }

        private FilterValidationResult ValidateFilterRequest(object request)
        {
            if (request is CreateFilterRequest createRequest)
            {
                if (string.IsNullOrWhiteSpace(createRequest.Name))
                {
                    return new FilterValidationResult { IsValid = false, ErrorMessage = "Filter name is required" };
                }

                if (createRequest.Type == "custom")
                {
                    var hasValidCondition = createRequest.Conditions?.Any(c => !string.IsNullOrWhiteSpace(c.Value)) == true;
                    var hasGmailFilters = createRequest.GmailFilters?.Labels?.Any() == true ||
                                         !string.IsNullOrWhiteSpace(createRequest.GmailFilters?.HasAttachment) ||
                                         !string.IsNullOrWhiteSpace(createRequest.CustomQuery);

                    if (!hasValidCondition && !hasGmailFilters)
                    {
                        return new FilterValidationResult
                        {
                            IsValid = false,
                            ErrorMessage = "At least one filter condition is required for custom filters"
                        };
                    }
                }
            }
            else if (request is UpdateFilterRequest updateRequest)
            {
                if (string.IsNullOrWhiteSpace(updateRequest.Name))
                {
                    return new FilterValidationResult { IsValid = false, ErrorMessage = "Filter name is required" };
                }
            }

            return new FilterValidationResult { IsValid = true };
        }

        #endregion
    }

    #region DTOs and Helper Classes

    public class CreateFilterRequest
    {
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = "custom";
        public string TemplateId { get; set; } = string.Empty;
        public bool IsActive { get; set; } = true;
        public string MatchCondition { get; set; } = "any";
        public List<FilterConditionDto>? Conditions { get; set; }
        public GmailFilterOptionsDto? GmailFilters { get; set; }
        public Dictionary<string, ExtractionRule>? ExtractionRules { get; set; }
        public string CustomQuery { get; set; } = string.Empty;
    }

    public class UpdateFilterRequest : CreateFilterRequest
    {
    }

    public class FilterConditionDto
    {
        public string Id { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string Operator { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
    }

    public class GmailFilterOptionsDto
    {
        public List<string>? Labels { get; set; }
        public string HasAttachment { get; set; } = string.Empty;
        public string IsRead { get; set; } = string.Empty;
        public string IsImportant { get; set; } = string.Empty;
        public string IsStarred { get; set; } = string.Empty;
        public DateRangeFilterDto? DateRange { get; set; }
    }

    public class DateRangeFilterDto
    {
        public string Type { get; set; } = string.Empty;
        public string Value { get; set; } = string.Empty;
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
    }

    public class FilterResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Name { get; set; } = string.Empty;
        public string Description { get; set; } = string.Empty;
        public string Type { get; set; } = string.Empty;
        public string TemplateId { get; set; } = string.Empty;
        public bool IsActive { get; set; }
        public string MatchCondition { get; set; } = string.Empty;
        public List<FilterConditionDto> Conditions { get; set; } = new();
        public GmailFilterOptionsDto? GmailFilters { get; set; }
        public Dictionary<string, ExtractionRule> ExtractionRules { get; set; } = new();
        public string CustomQuery { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime UpdatedAt { get; set; }
    }

    public class TestFilterRequest
    {
        public CreateFilterRequest Filter { get; set; } = new();
        public string SampleEmail { get; set; } = string.Empty;
    }

    public class FilterValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
    }

    #endregion
}

// Extension methods
public static class FilterExtensions
{
    public static FilterResponse ToFilterResponse(this EmailFilter filter)
    {
        return new FilterResponse
        {
            Id = filter.Id,
            Name = filter.Name,
            Description = filter.Description,
            Type = filter.Type,
            TemplateId = filter.TemplateId,
            IsActive = filter.IsActive,
            MatchCondition = filter.MatchCondition,
            Conditions = filter.Conditions?.Select(c => new FilterConditionDto
            {
                Id = c.Id,
                Type = c.Type,
                Operator = c.Operator,
                Value = c.Value
            }).ToList() ?? new List<FilterConditionDto>(),
            GmailFilters = filter.GmailFilters != null ? new GmailFilterOptionsDto
            {
                Labels = filter.GmailFilters.Labels,
                HasAttachment = filter.GmailFilters.HasAttachment,
                IsRead = filter.GmailFilters.IsRead,
                IsImportant = filter.GmailFilters.IsImportant,
                IsStarred = filter.GmailFilters.IsStarred,
                DateRange = filter.GmailFilters.DateRange != null ? new DateRangeFilterDto
                {
                    Type = filter.GmailFilters.DateRange.Type,
                    Value = filter.GmailFilters.DateRange.Value,
                    StartDate = filter.GmailFilters.DateRange.StartDate,
                    EndDate = filter.GmailFilters.DateRange.EndDate
                } : null
            } : null,
            ExtractionRules = filter.ExtractionRules ?? new Dictionary<string, ExtractionRule>(),
            CustomQuery = filter.CustomQuery,
            CreatedAt = filter.CreatedAt,
            UpdatedAt = filter.UpdatedAt
        };
    }
}