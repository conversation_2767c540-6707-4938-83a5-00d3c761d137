﻿using LSB.SellerMailTracker.API.Services.Interfaces;
using LSB.SellerMailTracker.API.Extensions;
using Microsoft.AspNetCore.Mvc;

namespace LSB.SellerMailTracker.API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class DashboardController : ControllerBase
    {
        private readonly IDataService _dataService;

        public DashboardController(IDataService dataService)
        {
            _dataService = dataService;
        }

        [HttpGet("stats")]
        public async Task<IActionResult> GetDashboardStats()
        {
            try
            {
                var stats = await _dataService.GetDashboardStatsAsync();
                var recentEmails = await _dataService.GetAllProcessedEmailsAsync();
                var filters = await _dataService.GetAllFiltersAsync();

                // Get recent emails (last 30 days)
                var thirtyDaysAgo = DateTime.UtcNow.AddDays(-30);
                recentEmails = recentEmails
                    .Where(e => e.EmailDate >= thirtyDaysAgo)
                    .OrderByDescending(e => e.EmailDate)
                    .ToList();

                var response = stats.ToResponse(recentEmails, filters);
                return Ok(response);
            }
            catch (Exception ex)
            {
                return BadRequest(new { error = ex.Message });
            }
        }
    }
}