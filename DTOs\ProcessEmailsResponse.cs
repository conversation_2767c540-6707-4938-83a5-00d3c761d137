﻿namespace LSB.SellerMailTracker.API.DTOs
{
    public class ProcessEmailsResponse
    {
        public int TotalProcessed { get; set; }
        public int NewEmails { get; set; }
        public int SkippedEmails { get; set; }
        public decimal TotalAmount { get; set; }
        public List<ProcessedEmailResponse> ProcessedEmails { get; set; } = new();
        public DateTime ProcessingStartTime { get; set; } = DateTime.UtcNow;
        public DateTime ProcessingEndTime { get; set; }

        public long ProcessingDuration { get; set; } 

        public Dictionary<string, object> AccountSummaries { get; set; } = new();
        public Dictionary<string, object> FilterSummaries { get; set; } = new();

        public Dictionary<string, CurrencySummary> CurrencySummaries { get; set; } = new();
    }
}