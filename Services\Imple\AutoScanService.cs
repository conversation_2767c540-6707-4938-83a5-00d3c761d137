﻿using System.Text.Json;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class AutoScanService : IAutoScanService
    {
        private readonly string _dataFolder;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly SemaphoreSlim _fileLock = new(1, 1);
        private readonly ILogger<AutoScanService> _logger;
        private readonly IDataService _dataService;
        private readonly IGmailService _gmailService;
        private readonly IEmailFilterService _filterService;
        private readonly ITemplateService _templateService;

        public AutoScanService(
            ILogger<AutoScanService> logger,
            IDataService dataService,
            IGmailService gmailService,
            IEmailFilterService filterService,
            ITemplateService templateService)
        {
            _logger = logger;
            _dataService = dataService;
            _gmailService = gmailService;
            _filterService = filterService;
            _templateService = templateService;

            _dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            Directory.CreateDirectory(_dataFolder);
            _logger.LogInformation($"🔧 AutoScanService initialized with data folder: {_dataFolder}");
        }

        #region Settings Management
        public async Task<AutoScanSettings?> GetAutoScanSettingsAsync()
        {
            var settingsFile = Path.Combine(_dataFolder, "autoscan_settings.json");

            if (!File.Exists(settingsFile))
            {
                _logger.LogInformation("📋 Auto-scan settings file not found, returning null");
                return null;
            }

            try
            {
                await _fileLock.WaitAsync();
                var json = await File.ReadAllTextAsync(settingsFile);
                var settings = JsonSerializer.Deserialize<AutoScanSettings>(json, _jsonOptions);

                _logger.LogInformation($"✅ Auto-scan settings loaded: Enabled={settings?.Enabled}, Accounts={settings?.Accounts?.Count ?? 0}");
                return settings;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error loading auto-scan settings");
                return null;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task SaveAutoScanSettingsAsync(AutoScanSettings settings)
        {
            await _fileLock.WaitAsync();
            try
            {
                var settingsFile = Path.Combine(_dataFolder, "autoscan_settings.json");
                settings.UpdatedAt = DateTime.UtcNow;

                // Generate ID if not exists
                if (string.IsNullOrEmpty(settings.Id))
                {
                    settings.Id = Guid.NewGuid().ToString();
                }

                var json = JsonSerializer.Serialize(settings, _jsonOptions);
                await File.WriteAllTextAsync(settingsFile, json);

                _logger.LogInformation($"💾 Auto-scan settings saved: ID={settings.Id}, Enabled={settings.Enabled}, Interval={settings.Interval}min, Accounts={settings.Accounts.Count}, Filters={settings.Filters.Count + settings.TemplateFilters.Count}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error saving auto-scan settings");
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task DeleteAutoScanSettingsAsync()
        {
            await _fileLock.WaitAsync();
            try
            {
                var settingsFile = Path.Combine(_dataFolder, "autoscan_settings.json");
                if (File.Exists(settingsFile))
                {
                    File.Delete(settingsFile);
                    _logger.LogInformation("🗑️ Auto-scan settings deleted");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error deleting auto-scan settings");
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }
        #endregion

        #region Logs Management
        public async Task SaveAutoScanLogAsync(AutoScanLog log)
        {
            await _fileLock.WaitAsync();
            try
            {
                var logs = await LoadAutoScanLogsAsync();

                // Ensure log has ID
                if (string.IsNullOrEmpty(log.Id))
                {
                    log.Id = Guid.NewGuid().ToString();
                }

                // Remove existing log with same ID (for updates)
                logs.RemoveAll(l => l.Id == log.Id);

                // Add new log
                logs.Add(log);

                // Keep only last 100 logs to prevent file from growing too large
                logs = logs.OrderByDescending(l => l.StartTime).Take(100).ToList();

                await SaveAutoScanLogsAsync(logs);

                var duration = log.EndTime.HasValue ? log.EndTime.Value - log.StartTime : TimeSpan.Zero;
                _logger.LogInformation($"📋 Auto-scan log saved: ID={log.Id}, Success={log.Success}, Duration={duration.TotalSeconds:F1}s, Emails={log.EmailsProcessed}, Amount=${log.TotalAmount}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error saving auto-scan log");
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task<List<AutoScanLog>> GetAutoScanLogsAsync(int limit = 20)
        {
            try
            {
                var logs = await LoadAutoScanLogsAsync();
                var result = logs.OrderByDescending(l => l.StartTime).Take(limit).ToList();
                _logger.LogDebug($"📋 Retrieved {result.Count} auto-scan logs (limit: {limit})");
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error loading auto-scan logs");
                return new List<AutoScanLog>();
            }
        }

        public async Task<AutoScanLog?> GetAutoScanLogAsync(string logId)
        {
            try
            {
                var logs = await LoadAutoScanLogsAsync();
                var log = logs.FirstOrDefault(l => l.Id == logId);
                _logger.LogDebug($"📋 Retrieved auto-scan log: {logId} - {(log != null ? "Found" : "Not found")}");
                return log;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error loading auto-scan log {logId}");
                return null;
            }
        }

        public async Task DeleteAutoScanLogsAsync(DateTime olderThan)
        {
            await _fileLock.WaitAsync();
            try
            {
                var logs = await LoadAutoScanLogsAsync();
                var initialCount = logs.Count;
                var filteredLogs = logs.Where(l => l.StartTime >= olderThan).ToList();

                await SaveAutoScanLogsAsync(filteredLogs);

                var deletedCount = initialCount - filteredLogs.Count;
                _logger.LogInformation($"🗑️ Deleted {deletedCount} auto-scan logs older than {olderThan:yyyy-MM-dd HH:mm}");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error deleting old auto-scan logs");
                throw;
            }
            finally
            {
                _fileLock.Release();
            }
        }

        private async Task<List<AutoScanLog>> LoadAutoScanLogsAsync()
        {
            var logsFile = Path.Combine(_dataFolder, "autoscan_logs.json");

            if (!File.Exists(logsFile))
                return new List<AutoScanLog>();

            try
            {
                var json = await File.ReadAllTextAsync(logsFile);
                return JsonSerializer.Deserialize<List<AutoScanLog>>(json, _jsonOptions) ?? new List<AutoScanLog>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error loading auto-scan logs from file");
                return new List<AutoScanLog>();
            }
        }

        private async Task SaveAutoScanLogsAsync(List<AutoScanLog> logs)
        {
            try
            {
                var logsFile = Path.Combine(_dataFolder, "autoscan_logs.json");
                var json = JsonSerializer.Serialize(logs, _jsonOptions);
                await File.WriteAllTextAsync(logsFile, json);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error saving auto-scan logs to file");
                throw;
            }
        }
        #endregion

        #region Auto-Scan Execution Logic
        public async Task<AutoScanResult> ExecuteAutoScanAsync(AutoScanSettings settings)
        {
            var result = new AutoScanResult
            {
                StartTime = DateTime.UtcNow,
                Success = false
            };

            var startTime = DateTime.UtcNow;
            var scanLog = new AutoScanLog
            {
                Id = Guid.NewGuid().ToString(),
                StartTime = startTime,
                AccountsConfigured = settings.Accounts?.Count ?? 0,
                Success = false
            };

            try
            {
                _logger.LogInformation($"🚀 Starting auto-scan execution with {settings.Accounts?.Count ?? 0} accounts and {(settings.Filters?.Count ?? 0) + (settings.TemplateFilters?.Count ?? 0)} filters");

                var errors = new List<string>();
                var processedAccounts = 0;
                var successfulAccounts = 0;
                var totalEmailsProcessed = 0;
                var totalAmount = 0m;
                var accountResults = new Dictionary<string, AccountProcessingResult>();

                // Get active accounts and validate
                var allAccounts = await _dataService.GetActiveAccountsAsync() ?? new List<GmailAccount>();
                if (!allAccounts.Any())
                {
                    errors.Add("No active accounts found in system");
                    _logger.LogWarning("⚠️ No active accounts available for auto-scan");
                    throw new InvalidOperationException("No active accounts available");
                }

                _logger.LogInformation($"📊 Available accounts: {allAccounts.Count}, Configured accounts: {settings.Accounts?.Count ?? 0}");

                // Process each configured account
                var accountIndex = 0;
                foreach (var accountId in settings.Accounts ?? new List<string>())
                {
                    accountIndex++;
                    AccountProcessingResult accountResult;

                    try
                    {
                        var account = allAccounts.FirstOrDefault(a => a.Id == accountId);
                        if (account == null)
                        {
                            var errorMsg = $"Account {accountId} not found or inactive";
                            errors.Add(errorMsg);
                            _logger.LogWarning($"⚠️ [{accountIndex}/{settings.Accounts.Count}] {errorMsg}");

                            accountResult = new AccountProcessingResult
                            {
                                ErrorMessage = errorMsg,
                                EmailsProcessed = 0,
                                TotalAmount = 0
                            };
                            accountResults[accountId] = accountResult;
                            continue;
                        }

                        _logger.LogInformation($"📧 [{accountIndex}/{settings.Accounts.Count}] Processing account: {account.Email} (ID: {accountId})");

                        // ✅ Process single account with detailed logging
                        accountResult = await ProcessSingleAccountForAutoScan(account, settings, accountIndex);
                        accountResults[accountId] = accountResult;

                        // ✅ Update counters regardless of success/failure
                        processedAccounts++;
                        totalEmailsProcessed += accountResult.EmailsProcessed;
                        totalAmount += accountResult.TotalAmount;

                        if (string.IsNullOrEmpty(accountResult.ErrorMessage))
                        {
                            successfulAccounts++;
                            _logger.LogInformation($"✅ [{accountIndex}/{settings.Accounts.Count}] Account {account.Email} completed successfully: {accountResult.EmailsProcessed} emails, ${accountResult.TotalAmount:F2}");
                        }
                        else
                        {
                            errors.Add($"{account.Email}: {accountResult.ErrorMessage}");
                            _logger.LogWarning($"⚠️ [{accountIndex}/{settings.Accounts.Count}] Account {account.Email} completed with errors: {accountResult.ErrorMessage}");
                        }
                    }
                    catch (Exception accountEx)
                    {
                        var errorMsg = $"Critical error processing account {accountId}: {accountEx.Message}";
                        errors.Add(errorMsg);
                        _logger.LogError(accountEx, $"❌ [{accountIndex}/{settings.Accounts.Count}] {errorMsg}");

                        accountResult = new AccountProcessingResult
                        {
                            ErrorMessage = accountEx.Message,
                            EmailsProcessed = 0,
                            TotalAmount = 0
                        };
                        accountResults[accountId] = accountResult;

                        // ✅ Continue processing other accounts even if one fails
                        processedAccounts++;
                    }
                }

                // ✅ Enhanced result calculation
                var overallSuccess = successfulAccounts > 0 && errors.Count == 0;
                var partialSuccess = successfulAccounts > 0 && errors.Count > 0;

                // Update scan log with detailed information
                scanLog.EndTime = DateTime.UtcNow;
                scanLog.Success = overallSuccess || partialSuccess; // ✅ Consider partial success as success
                scanLog.AccountsProcessed = processedAccounts;
                scanLog.EmailsProcessed = totalEmailsProcessed;
                scanLog.TotalAmount = totalAmount;
                scanLog.ErrorMessage = errors.Any() ?
                    $"Successful: {successfulAccounts}/{processedAccounts} accounts. Errors: " +
                    string.Join("; ", errors.Take(2)) + (errors.Count > 2 ? "..." : "") : null;

                // ✅ Add metadata to log
                scanLog.Metadata = new Dictionary<string, object>
                {
                    { "successfulAccounts", successfulAccounts },
                    { "failedAccounts", processedAccounts - successfulAccounts },
                    { "configuredAccounts", settings.Accounts?.Count ?? 0 },
                    { "filtersUsed", (settings.Filters?.Count ?? 0) + (settings.TemplateFilters?.Count ?? 0) },
                    { "accountResults", accountResults }
                };

                // Save log
                await SaveAutoScanLogAsync(scanLog);

                // ✅ Update settings with last run time (only update if we processed accounts)
                if (processedAccounts > 0)
                {
                    settings.LastRun = DateTime.UtcNow;
                    await SaveAutoScanSettingsAsync(settings);
                }

                // Set result
                result.Success = scanLog.Success;
                result.EndTime = DateTime.UtcNow;
                result.AccountsProcessed = processedAccounts;
                result.EmailsProcessed = totalEmailsProcessed;
                result.TotalAmount = totalAmount;
                result.ErrorMessage = scanLog.ErrorMessage;

                _logger.LogInformation($"🎉 Auto-scan execution completed: {successfulAccounts}/{processedAccounts} accounts successful, {totalEmailsProcessed} emails, ${totalAmount:F2}, Overall Success: {result.Success}");

                return result;
            }
            catch (Exception ex)
            {
                scanLog.EndTime = DateTime.UtcNow;
                scanLog.Success = false;
                scanLog.ErrorMessage = $"Critical auto-scan failure: {ex.Message}";
                await SaveAutoScanLogAsync(scanLog);

                result.Success = false;
                result.EndTime = DateTime.UtcNow;
                result.ErrorMessage = ex.Message;

                _logger.LogError(ex, "❌ Critical error in auto-scan execution");
                return result;
            }
        }

        private async Task<AccountProcessingResult> ProcessSingleAccountForAutoScan(GmailAccount account, AutoScanSettings settings, int accountIndex = 0)
        {
            var result = new AccountProcessingResult();
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();

            try
            {
                _logger.LogDebug($"🔐 [{accountIndex}] Checking token for {account.Email}...");

                // Check token expiry
                if (DateTime.UtcNow >= account.TokenExpiry)
                {
                    _logger.LogWarning($"⚠️ [{accountIndex}] Token expired for {account.Email}, attempting refresh...");
                    var refreshSuccess = await _gmailService.RefreshTokenAsync(account.Id);
                    if (!refreshSuccess)
                    {
                        result.ErrorMessage = $"Failed to refresh token for {account.Email}";
                        return result;
                    }
                    _logger.LogInformation($"✅ [{accountIndex}] Token refreshed successfully for {account.Email}");
                }

                // Get filters for processing
                _logger.LogDebug($"🎯 [{accountIndex}] Loading filters for {account.Email}...");
                var filters = await GetFiltersForAutoScan(settings);
                if (!filters.Any())
                {
                    result.ErrorMessage = "No valid filters available";
                    return result;
                }
                _logger.LogDebug($"🎯 [{accountIndex}] Loaded {filters.Count} filters for {account.Email}");

                // Get emails from last scan or last 24 hours
                var fromDate = settings.LastRun ?? DateTime.UtcNow.AddDays(-1);
                _logger.LogDebug($"📧 [{accountIndex}] Retrieving emails for {account.Email} from {fromDate:yyyy-MM-dd HH:mm}...");

                var emails = await _gmailService.GetEmailsAsync(account.Id, fromDate, null);
                _logger.LogInformation($"📧 [{accountIndex}] Retrieved {emails.Count} emails for {account.Email} from {fromDate:yyyy-MM-dd HH:mm}");

                if (emails.Count == 0)
                {
                    _logger.LogInformation($"📧 [{accountIndex}] No emails to process for {account.Email}");
                    return result; // No emails to process
                }

                // Apply filters
                _logger.LogDebug($"🎯 [{accountIndex}] Applying filters to {emails.Count} emails for {account.Email}...");
                var processedEmails = await _filterService.ProcessEmailsAsync(emails, filters, account.Email);
                _logger.LogInformation($"🎯 [{accountIndex}] Filtered {emails.Count} → {processedEmails.Count} emails for {account.Email}");

                if (processedEmails.Count == 0)
                {
                    _logger.LogInformation($"🎯 [{accountIndex}] No emails matched filters for {account.Email}");
                    return result;
                }

                // ✅ Save emails with detailed tracking
                var newEmailsCount = 0;
                var duplicateEmailsCount = 0;
                var saveErrors = new List<string>();

                _logger.LogDebug($"💾 [{accountIndex}] Saving {processedEmails.Count} processed emails for {account.Email}...");

                foreach (var processedEmail in processedEmails)
                {
                    try
                    {
                        // Apply amount sign based on filter type
                        ApplyAmountSignBasedOnFilterType(processedEmail, filters);

                        // Check if email already exists
                        var existingEmail = await _dataService.GetProcessedEmailByGmailIdAsync(processedEmail.GmailId);
                        if (existingEmail == null)
                        {
                            // ✅ Ensure account email is set
                            processedEmail.AccountEmail = account.Email;

                            await _dataService.SaveProcessedEmailAsync(processedEmail);
                            newEmailsCount++;
                            result.TotalAmount += processedEmail.Amount;

                            _logger.LogTrace($"💾 [{accountIndex}] Saved new email: {processedEmail.Subject} (${processedEmail.Amount:F2}) for {account.Email}");
                        }
                        else
                        {
                            duplicateEmailsCount++;
                            _logger.LogTrace($"⏭️ [{accountIndex}] Skipped duplicate email: {processedEmail.Subject} for {account.Email}");
                        }
                    }
                    catch (Exception emailEx)
                    {
                        var emailError = $"Error saving email '{processedEmail.Subject}': {emailEx.Message}";
                        saveErrors.Add(emailError);
                        _logger.LogError(emailEx, $"❌ [{accountIndex}] {emailError} for {account.Email}");
                    }
                }

                result.EmailsProcessed = newEmailsCount;

                stopwatch.Stop();
                _logger.LogInformation($"💾 [{accountIndex}] Account {account.Email} processing completed in {stopwatch.ElapsedMilliseconds}ms:");
                _logger.LogInformation($"    • New emails saved: {newEmailsCount}");
                _logger.LogInformation($"    • Duplicate emails skipped: {duplicateEmailsCount}");
                _logger.LogInformation($"    • Total amount: ${result.TotalAmount:F2}");
                _logger.LogInformation($"    • Save errors: {saveErrors.Count}");

                // ✅ Set error message if there were save errors
                if (saveErrors.Any())
                {
                    result.ErrorMessage = $"Saved {newEmailsCount} emails with {saveErrors.Count} errors: " +
                                        string.Join("; ", saveErrors.Take(2)) + (saveErrors.Count > 2 ? "..." : "");
                }
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                result.ErrorMessage = ex.Message;
                _logger.LogError(ex, $"❌ [{accountIndex}] Critical error processing account {account.Email} in auto-scan (took {stopwatch.ElapsedMilliseconds}ms)");
            }

            return result;
        }

        private async Task<List<EmailFilter>> GetFiltersForAutoScan(AutoScanSettings settings)
        {
            var filters = new List<EmailFilter>();

            // Add custom filters
            if (settings.Filters?.Any() == true)
            {
                _logger.LogDebug($"🎯 Loading {settings.Filters.Count} custom filters...");
                foreach (var filterId in settings.Filters)
                {
                    try
                    {
                        var filter = await _dataService.GetFilterAsync(filterId);
                        if (filter != null && filter.IsActive)
                        {
                            filters.Add(filter);
                            _logger.LogTrace($"✅ Loaded custom filter: {filter.Name}");
                        }
                        else
                        {
                            _logger.LogWarning($"⚠️ Custom filter {filterId} not found or inactive");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ Error loading custom filter {filterId}");
                    }
                }
            }

            // Add template filters
            if (settings.TemplateFilters?.Any() == true)
            {
                _logger.LogDebug($"🎯 Loading {settings.TemplateFilters.Count} template filters...");
                foreach (var templateId in settings.TemplateFilters)
                {
                    try
                    {
                        var templateFilter = await GetTemplateFilterForAutoScan(templateId);
                        if (templateFilter != null)
                        {
                            filters.Add(templateFilter);
                            _logger.LogTrace($"✅ Loaded template filter: {templateFilter.Name}");
                        }
                        else
                        {
                            _logger.LogWarning($"⚠️ Template filter {templateId} not found");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, $"❌ Error loading template filter {templateId}");
                    }
                }
            }

            _logger.LogInformation($"🎯 Total filters loaded for auto-scan: {filters.Count} (Custom: {settings.Filters?.Count ?? 0}, Templates: {settings.TemplateFilters?.Count ?? 0})");
            return filters;
        }

        private async Task<EmailFilter?> GetTemplateFilterForAutoScan(string templateId)
        {
            try
            {
                var template = _templateService.GetEmailTemplate(templateId);
                if (template == null)
                {
                    _logger.LogWarning($"⚠️ Template {templateId} not found");
                    return null;
                }

                var filter = new EmailFilter
                {
                    Id = Guid.NewGuid().ToString(),
                    TemplateId = template.Id,
                    Name = template.Name,
                    Type = template.Type,
                    Description = $"Auto-scan template: {template.Name}",
                    IsActive = true,
                    MatchCondition = template.Platform switch
                    {
                        "PayPal" => "all",
                        "Payoneer" => "any",
                        _ => "any"
                    },
                    CreatedAt = DateTime.UtcNow,
                    UpdatedAt = DateTime.UtcNow,
                    Conditions = template.Conditions.Select(c => new FilterCondition
                    {
                        Id = Guid.NewGuid().ToString(),
                        Type = c.Type,
                        Operator = c.Operator,
                        Value = c.Value
                    }).ToList(),
                    ExtractionRules = new Dictionary<string, ExtractionRule>(template.ExtractionRules)
                };

                _logger.LogTrace($"✅ Created filter from template: {filter.Name} (Type: {filter.Type})");
                return filter;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error getting template filter {templateId} for auto-scan");
                return null;
            }
        }

        private void ApplyAmountSignBasedOnFilterType(ProcessedEmail email, List<EmailFilter> filters)
        {
            try
            {
                var matchedFilter = filters.FirstOrDefault(f => f.Id == email.FilterId || f.Name == email.FilterName);

                if (matchedFilter != null)
                {
                    var originalAmount = email.Amount;
                    switch (matchedFilter.Type?.ToLower())
                    {
                        case "income":
                            email.Amount = Math.Abs(email.Amount);
                            break;
                        case "outcome":
                            email.Amount = -Math.Abs(email.Amount);
                            break;
                    }

                    if (originalAmount != email.Amount)
                    {
                        _logger.LogTrace($"💰 Applied {matchedFilter.Type} sign: {originalAmount:F2} → {email.Amount:F2} for '{email.Subject}'");
                    }
                }
                else
                {
                    _logger.LogWarning($"⚠️ No matching filter found for email '{email.Subject}' (FilterId: {email.FilterId}, FilterName: {email.FilterName})");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"❌ Error applying amount sign for email {email.Subject}");
            }
        }
        #endregion
        public async Task<int> CleanupOldLogsAsync(int retentionDays = 30)
        {
            try
            {
                var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);
                var logs = await LoadAutoScanLogsAsync();
                var initialCount = logs.Count;

                var filteredLogs = logs.Where(l => l.StartTime >= cutoffDate).ToList();
                await SaveAutoScanLogsAsync(filteredLogs);

                var deletedCount = initialCount - filteredLogs.Count;
                _logger.LogInformation($"🧹 Cleanup completed: Deleted {deletedCount} logs older than {retentionDays} days");

                return deletedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during log cleanup");
                return 0;
            }
        }

        public async Task<AutoScanStatistics> GetAutoScanStatisticsAsync()
        {
            try
            {
                var logs = await LoadAutoScanLogsAsync();
                var settings = await GetAutoScanSettingsAsync();

                var stats = new AutoScanStatistics
                {
                    TotalRuns = logs.Count,
                    SuccessfulRuns = logs.Count(l => l.Success),
                    FailedRuns = logs.Count(l => !l.Success),
                    TotalEmailsProcessed = logs.Sum(l => l.EmailsProcessed),
                    TotalAmount = logs.Sum(l => l.TotalAmount),
                    AverageEmailsPerRun = logs.Any() ? logs.Average(l => l.EmailsProcessed) : 0,
                    AverageAmountPerRun = logs.Any() ? logs.Average(l => l.TotalAmount) : 0,
                    LastRunTime = settings?.LastRun,
                    NextRunTime = settings?.NextRun,
                    IsEnabled = settings?.Enabled ?? false,
                    CurrentInterval = settings?.Interval ?? 60,
                    ConfiguredAccounts = settings?.Accounts?.Count ?? 0,
                    ConfiguredFilters = (settings?.Filters?.Count ?? 0) + (settings?.TemplateFilters?.Count ?? 0)
                };

                return stats;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error calculating auto-scan statistics");
                return new AutoScanStatistics();
            }
        }
    }

    // Helper classes
    public class AccountProcessingResult
    {
        public int EmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public string? ErrorMessage { get; set; }
    }
}