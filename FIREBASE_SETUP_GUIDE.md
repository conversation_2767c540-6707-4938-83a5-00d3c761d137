# Firebase Setup Guide

## Bước 1: Tạo Firebase Project

1. <PERSON><PERSON><PERSON> cậ<PERSON> [Firebase Console](https://console.firebase.google.com/)
2. Click "Add project" hoặc "Create a project"
3. Nhập tên project (ví dụ: `lsb-seller-mail-tracker`)
4. Chọn các tùy chọn phù hợp và tạo project

## Bước 2: Tạo Firestore Database

1. Trong Firebase Console, chọn project vừa tạo
2. Vào "Firestore Database" từ menu bên trái
3. Click "Create database"
4. Chọn "Start in test mode" (có thể thay đổi sau)
5. Chọn location gần nhất (ví dụ: asia-southeast1)

## Bước 3: Tạo Service Account

1. Vào "Project Settings" (icon bánh răng)
2. Chọn tab "Service accounts"
3. Click "Generate new private key"
4. Download file JSON và đặt tên là `firebase-service-account.json`
5. Copy file này và<PERSON> thư mục root của project

## Bước 4: Cập nhật appsettings.json

```json
{
  "Firebase": {
    "ProjectId": "your-firebase-project-id",
    "ServiceAccountKeyPath": "firebase-service-account.json",
    "DatabaseUrl": "https://your-firebase-project-id-default-rtdb.firebaseio.com/"
  }
}
```

**Lưu ý:** Thay `your-firebase-project-id` bằng Project ID thực tế của bạn.

## Bước 5: Cấu hình Firestore Security Rules

Trong Firestore Console, vào "Rules" và cập nhật:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Allow read/write access to all documents
    // Trong production, nên cấu hình rules chặt chẽ hơn
    match /{document=**} {
      allow read, write: if true;
    }
  }
}
```

## Bước 6: Cấu trúc Collections trong Firestore

API sẽ tự động tạo các collections sau:

### 1. `users` Collection
```json
{
  "id": "user-guid",
  "email": "<EMAIL>",
  "name": "User Name",
  "passwordHash": "hashed-password",
  "role": "User|Admin",
  "createdAt": "2024-01-01T00:00:00Z",
  "lastLoginAt": "2024-01-01T00:00:00Z",
  "isActive": true,
  "refreshToken": "refresh-token",
  "refreshTokenExpiryTime": "2024-01-08T00:00:00Z"
}
```

### 2. `gmail_accounts` Collection
```json
{
  "id": "account-guid",
  "email": "<EMAIL>",
  "displayName": "Display Name",
  "refreshToken": "oauth-refresh-token",
  "accessToken": "oauth-access-token",
  "tokenExpiry": "2024-01-01T01:00:00Z",
  "createdAt": "2024-01-01T00:00:00Z",
  "lastSyncAt": "2024-01-01T00:00:00Z",
  "isActive": true,
  "status": "active",
  "sellerInfo": {
    "name": "Seller Name",
    "description": "Seller Description"
  }
}
```

### 3. `email_filters` Collection
```json
{
  "id": "filter-guid",
  "name": "Filter Name",
  "description": "Filter Description",
  "type": "custom",
  "templateId": "",
  "isActive": true,
  "matchCondition": "any",
  "conditions": [],
  "gmailFilters": {},
  "extractionRules": {},
  "customQuery": ""
}
```

### 4. `processed_emails` Collection
```json
{
  "id": "email-guid",
  "gmailId": "gmail-message-id",
  "accountEmail": "<EMAIL>",
  "filterId": "filter-guid",
  "subject": "Email Subject",
  "from": "<EMAIL>",
  "to": "<EMAIL>",
  "date": "2024-01-01T00:00:00Z",
  "processedAt": "2024-01-01T00:00:00Z",
  "extractedData": {},
  "rawContent": "email content"
}
```

## Bước 7: Test Firebase Connection

1. Chạy project: `dotnet run`
2. Kiểm tra logs để đảm bảo Firebase khởi tạo thành công
3. Test đăng ký user mới qua API
4. Kiểm tra Firestore Console để xem dữ liệu đã được lưu

## Bước 8: Bảo mật (Production)

### Environment Variables
Thay vì hardcode trong appsettings.json, sử dụng environment variables:

```json
{
  "Firebase": {
    "ProjectId": "${FIREBASE_PROJECT_ID}",
    "ServiceAccountKeyPath": "${FIREBASE_SERVICE_ACCOUNT_PATH}",
    "DatabaseUrl": "${FIREBASE_DATABASE_URL}"
  }
}
```

### Firestore Security Rules (Production)
```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users can only access their own data
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Gmail accounts - restrict access
    match /gmail_accounts/{accountId} {
      allow read, write: if request.auth != null;
    }
    
    // Email filters - restrict access
    match /email_filters/{filterId} {
      allow read, write: if request.auth != null;
    }
    
    // Processed emails - restrict access
    match /processed_emails/{emailId} {
      allow read, write: if request.auth != null;
    }
  }
}
```

## Troubleshooting

### Lỗi thường gặp:

1. **"Service account key not found"**
   - Kiểm tra đường dẫn file `firebase-service-account.json`
   - Đảm bảo file có quyền đọc

2. **"Permission denied"**
   - Kiểm tra Firestore Security Rules
   - Đảm bảo Service Account có quyền truy cập

3. **"Project not found"**
   - Kiểm tra Project ID trong appsettings.json
   - Đảm bảo project đã được tạo trong Firebase Console

### Logs để debug:
```csharp
_logger.LogInformation("Firebase service initialized successfully");
_logger.LogError(ex, "Failed to initialize Firebase service");
```

## Migration từ JSON Files

Nếu bạn đã có dữ liệu trong JSON files, có thể tạo script migration để chuyển dữ liệu sang Firebase:

1. Đọc dữ liệu từ JSON files
2. Convert sang models tương ứng
3. Sử dụng Firebase batch operations để import
4. Verify dữ liệu đã được import đúng
