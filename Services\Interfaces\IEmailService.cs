﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IEmailService
    {
        Task SaveProcessedEmailAsync(ProcessedEmail email);
        Task SaveProcessedEmailsBatchAsync(List<ProcessedEmail> emails);
        Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId);
        Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId);
        Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync();
        Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail);
        Task<List<ProcessedEmail>> GetProcessedEmailsByAccountIdAsync(string accountId);
        Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId);
        Task<List<ProcessedEmail>> GetRecentProcessedEmailsAsync(int days = 30);
        Task<bool> EmailExistsAsync(string gmailId);
        Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds);
        Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page,
            int pageSize,
            string? accountEmail = null,
            string? filterId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null);
    }
}