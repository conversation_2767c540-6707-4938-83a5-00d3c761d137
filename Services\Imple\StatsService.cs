﻿using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class StatsService : IStatsService
    {
        private readonly IAccountService _accountService;
        private readonly IFilterService _filterService;
        private readonly IEmailService _emailService;
        private readonly ILogger<StatsService>? _logger;

        public StatsService(
            IAccountService accountService,
            IFilterService filterService,
            IEmailService emailService,
            ILogger<StatsService>? logger = null)
        {
            _accountService = accountService;
            _filterService = filterService;
            _emailService = emailService;
            _logger = logger;
        }

        public async Task<DashboardStats> GetDashboardStatsAsync()
        {
            try
            {
                var accounts = await _accountService.GetAllAccountsAsync();
                var filters = await _filterService.GetAllFiltersAsync();
                var emails = await _emailService.GetAllProcessedEmailsAsync();

                var stats = new DashboardStats
                {
                    TotalAccounts = accounts.Count(a => a.IsActive),
                    ActiveFilters = filters.Count(f => f.IsActive),
                    TotalEmailsProcessed = emails.Count,
                    TotalAmount = emails.Sum(e => e.Amount),
                    LastProcessedDate = emails.Any() ? emails.Max(e => e.ProcessedAt) : DateTime.MinValue
                };

                _logger?.LogInformation($"Dashboard stats calculated: {stats.TotalEmailsProcessed} emails, {stats.TotalAmount} total amount");
                return stats;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calculating dashboard stats");
                throw;
            }
        }

        public async Task<Dictionary<string, CurrencyStats>> GetCurrencyStatsAsync(int days = 30)
        {
            try
            {
                var emails = await _emailService.GetRecentProcessedEmailsAsync(days);

                var currencyStats = emails
                    .GroupBy(e => e.Currency)
                    .ToDictionary(g => g.Key, g => new CurrencyStats
                    {
                        Currency = g.Key,
                        TotalAmount = g.Sum(e => e.Amount),
                        EmailCount = g.Count(),
                        AverageAmount = g.Any() ? g.Average(e => e.Amount) : 0,
                        MinAmount = g.Any() ? g.Min(e => e.Amount) : 0,
                        MaxAmount = g.Any() ? g.Max(e => e.Amount) : 0,
                        LastTransactionDate = g.Any() ? g.Max(e => e.EmailDate) : DateTime.MinValue
                    });

                _logger?.LogInformation($"Currency stats calculated for {days} days: {currencyStats.Count} currencies");
                return currencyStats;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calculating currency stats");
                throw;
            }
        }

        public async Task<ProcessingStatsResult> GetProcessingStatsAsync(DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                var emails = await _emailService.GetAllProcessedEmailsAsync();

                if (fromDate.HasValue)
                    emails = emails.Where(e => e.ProcessedAt >= fromDate.Value).ToList();

                if (toDate.HasValue)
                    emails = emails.Where(e => e.ProcessedAt <= toDate.Value).ToList();

                var today = DateTime.UtcNow.Date;
                var thisWeek = today.AddDays(-(int)today.DayOfWeek);
                var thisMonth = new DateTime(today.Year, today.Month, 1);

                var emailsToday = emails.Where(e => e.EmailDate.Date == today).ToList();
                var emailsThisWeek = emails.Where(e => e.EmailDate.Date >= thisWeek).ToList();
                var emailsThisMonth = emails.Where(e => e.EmailDate.Date >= thisMonth).ToList();

                var stats = new ProcessingStatsResult
                {
                    TotalEmails = emails.Count,
                    TotalAmount = emails.Sum(e => e.Amount),
                    AverageAmount = emails.Any() ? emails.Average(e => e.Amount) : 0,

                    EmailsToday = emailsToday.Count,
                    EmailsThisWeek = emailsThisWeek.Count,
                    EmailsThisMonth = emailsThisMonth.Count,

                    AmountToday = emailsToday.Sum(e => e.Amount),
                    AmountThisWeek = emailsThisWeek.Sum(e => e.Amount),
                    AmountThisMonth = emailsThisMonth.Sum(e => e.Amount),

                    FirstEmailDate = emails.Any() ? emails.Min(e => e.EmailDate) : DateTime.MinValue,
                    LastEmailDate = emails.Any() ? emails.Max(e => e.EmailDate) : DateTime.MinValue
                };

                _logger?.LogInformation($"Processing stats calculated: {stats.TotalEmails} total emails");
                return stats;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error calculating processing stats");
                throw;
            }
        }
    }
}