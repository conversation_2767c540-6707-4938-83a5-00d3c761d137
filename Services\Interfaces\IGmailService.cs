﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IGmailService : IDisposable
    {
        /// <summary>
        /// Get OAuth 2.0 authorization URL for Gmail access
        /// </summary>
        /// <returns>Authorization URL string</returns>
        Task<string> GetAuthorizationUrlAsync();

        /// <summary>
        /// Get OAuth 2.0 authorization URL for Gmail access with state data
        /// </summary>
        /// <param name="stateData">State data to embed in the authorization URL</param>
        /// <returns>Authorization URL string</returns>
        Task<string> GetAuthorizationUrlAsync(object stateData);

        /// <summary>
        /// Process OAuth callback and return account information
        /// </summary>
        /// <param name="authorizationCode">Authorization code from OAuth callback</param>
        /// <param name="state">State parameter from OAuth callback</param>
        /// <returns>Gmail account information</returns>
        Task<GmailAccount> ProcessAuthCallbackAsync(string authorizationCode, string state);

        /// <summary>
        /// Add new Gmail account using authorization code from OAuth flow
        /// </summary>
        /// <param name="authorizationCode">Authorization code from OAuth callback</param>
        /// <returns>Created Gmail account</returns>
        Task<GmailAccount> AddAccountAsync(string authorizationCode);

        /// <summary>
        /// Get emails from Gmail account with improved retrieval and filtering
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <param name="fromDate">Start date for email range (null = no limit)</param>
        /// <param name="toDate">End date for email range (null = no limit)</param>
        /// <returns>List of Gmail messages</returns>
        Task<List<GmailMessage>> GetEmailsAsync(string accountId, DateTime? fromDate = null, DateTime? toDate = null);

        /// <summary>
        /// Refresh expired access token using refresh token
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <returns>True if refresh successful, false otherwise</returns>
        Task<bool> RefreshTokenAsync(string accountId);

        /// <summary>
        /// Get email address associated with access token
        /// </summary>
        /// <param name="accessToken">Gmail access token</param>
        /// <returns>Email address string</returns>
        Task<string> GetAccountEmailAsync(string accessToken);

        /// <summary>
        /// Get emails using specific query with improved error handling
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <param name="query">Gmail search query</param>
        /// <param name="maxResults">Maximum number of results to retrieve</param>
        /// <returns>List of Gmail messages</returns>
        Task<List<GmailMessage>> GetEmailsByQueryAsync(string accountId, string query, int maxResults = 1000);

        /// <summary>
        /// Validate if account token is still valid and not expired
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <returns>True if token is valid, false if expired or invalid</returns>
        Task<bool> ValidateTokenAsync(string accountId);

        /// <summary>
        /// Get Gmail account labels/folders
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <returns>List of Gmail labels</returns>
        Task<List<GmailLabel>> GetLabelsAsync(string accountId);

        /// <summary>
        /// Get account quota and usage information
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <returns>Account quota information</returns>
        Task<GmailQuotaInfo> GetAccountQuotaAsync(string accountId);

        /// <summary>
        /// Batch process multiple accounts for email retrieval
        /// </summary>
        /// <param name="accountIds">List of account IDs to process</param>
        /// <param name="fromDate">Start date for email range</param>
        /// <param name="toDate">End date for email range</param>
        /// <returns>Dictionary of account results</returns>
        //Task<Dictionary<string, AccountProcessingResult>> BatchProcessAccountsAsync(
        //    List<string> accountIds,
        //    DateTime? fromDate = null,
        //    DateTime? toDate = null);

        /// <summary>
        /// Get processing statistics for an account
        /// </summary>
        /// <param name="accountId">Gmail account ID</param>
        /// <returns>Account processing statistics</returns>
        Task<AccountProcessingStats> GetAccountStatsAsync(string accountId);
    }

    // Supporting models for the interface
    public class GmailLabel
    {
        public string Id { get; set; } = "";
        public string Name { get; set; } = "";
        public string Type { get; set; } = "";
        public int MessageListVisibility { get; set; }
        public int LabelListVisibility { get; set; }
    }

    public class GmailQuotaInfo
    {
        public long StorageUsed { get; set; }
        public long StorageLimit { get; set; }
        public double StoragePercentage => StorageLimit > 0 ? (double)StorageUsed / StorageLimit * 100 : 0;
        public int EmailCount { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public class AccountProcessingResult
    {
        public string AccountId { get; set; } = "";
        public string AccountEmail { get; set; } = "";
        public bool Success { get; set; }
        public string? ErrorMessage { get; set; }
        public int EmailsRetrieved { get; set; }
        public int EmailsProcessed { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public bool TokenRefreshed { get; set; }
        public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    }

    public class AccountProcessingStats
    {
        public string AccountId { get; set; } = "";
        public string AccountEmail { get; set; } = "";
        public int TotalEmailsRetrieved { get; set; }
        public int TotalEmailsProcessed { get; set; }
        public DateTime LastProcessedDate { get; set; }
        public TimeSpan TotalProcessingTime { get; set; }
        public int ProcessingRuns { get; set; }
        public double AverageEmailsPerRun => ProcessingRuns > 0 ? (double)TotalEmailsRetrieved / ProcessingRuns : 0;
        public bool IsHealthy { get; set; }
        public List<string> HealthIssues { get; set; } = new();
    }
}