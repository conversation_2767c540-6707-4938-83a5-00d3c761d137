using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IUserService
    {
        Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request);
        Task<ApiResponse<LoginResponse>> RegisterAsync(RegisterRequest request);
        Task<ApiResponse<LoginResponse>> RefreshTokenAsync(RefreshTokenRequest request);
        Task<ApiResponse<object>> LogoutAsync(string userId);
        Task<User?> GetUserByEmailAsync(string email);
        Task<User?> GetUserByIdAsync(string id);
        Task<bool> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> VerifyPasswordAsync(string password, string hash);
        string HashPassword(string password);
    }
}
