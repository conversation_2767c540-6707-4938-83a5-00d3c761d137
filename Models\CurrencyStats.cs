﻿// ✅ Tạo file mới: Models/DataServiceModels.cs

namespace LSB.SellerMailTracker.API.Models
{
    public class CurrencyStats
    {
        public string Currency { get; set; } = "";
        public decimal TotalAmount { get; set; }
        public int EmailCount { get; set; }
        public decimal AverageAmount { get; set; }
        public decimal MinAmount { get; set; }
        public decimal MaxAmount { get; set; }
        public DateTime LastTransactionDate { get; set; }
    }

    public class ProcessingStatsResult
    {
        public int TotalEmails { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal AverageAmount { get; set; }

        public int EmailsToday { get; set; }
        public int EmailsThisWeek { get; set; }
        public int EmailsThisMonth { get; set; }

        public decimal AmountToday { get; set; }
        public decimal AmountThisWeek { get; set; }
        public decimal AmountThisMonth { get; set; }

        public DateTime FirstEmailDate { get; set; }
        public DateTime LastEmailDate { get; set; }
    }
}