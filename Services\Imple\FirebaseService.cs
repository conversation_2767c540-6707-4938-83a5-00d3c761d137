using FirebaseAdmin;
using Google.Cloud.Firestore;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using Microsoft.Extensions.Options;
using System.Text.Json;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FirebaseService : IFirebaseService
    {
        private readonly FirestoreDb _firestoreDb;
        private readonly ILogger<FirebaseService> _logger;
        private readonly FirebaseSettings _firebaseSettings;

        // Collection names
        private const string USERS_COLLECTION = "users";
        private const string ACCOUNTS_COLLECTION = "gmail_accounts";
        private const string FILTERS_COLLECTION = "email_filters";
        private const string EMAILS_COLLECTION = "processed_emails";
        private const string SETTINGS_COLLECTION = "settings";

        // Fixed document IDs for singleton settings
        private const string AUTO_SCAN_SETTINGS_DOC = "auto_scan_settings";

        public FirebaseService(IOptions<FirebaseSettings> firebaseSettings, ILogger<FirebaseService> logger)
        {
            _firebaseSettings = firebaseSettings.Value;
            _logger = logger;

            try
            {
                // Initialize Firebase if not already initialized
                if (FirebaseApp.DefaultInstance == null)
                {
                    var credential = Google.Apis.Auth.OAuth2.GoogleCredential.FromFile(_firebaseSettings.ServiceAccountKeyPath);
                    FirebaseApp.Create(new AppOptions()
                    {
                        Credential = credential,
                        ProjectId = _firebaseSettings.ProjectId
                    });
                }

                _firestoreDb = FirestoreDb.Create(_firebaseSettings.ProjectId);
                _logger.LogInformation("Firebase service initialized successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize Firebase service");
                throw;
            }
        }

        #region User Operations

        public async Task<User?> GetUserAsync(string userId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(USERS_COLLECTION).Document(userId);
                var snapshot = await docRef.GetSnapshotAsync();
                
                if (snapshot.Exists)
                {
                    return snapshot.ConvertTo<User>();
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user {UserId}", userId);
                return null;
            }
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            try
            {
                var query = _firestoreDb.Collection(USERS_COLLECTION)
                    .WhereEqualTo("Email", email)
                    .Limit(1);
                
                var snapshot = await query.GetSnapshotAsync();
                var user = snapshot.Documents.FirstOrDefault()?.ConvertTo<User>();
                return user;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting user by email {Email}", email);
                return null;
            }
        }

        public async Task<string> CreateUserAsync(User user)
        {
            try
            {
                var docRef = _firestoreDb.Collection(USERS_COLLECTION).Document(user.Id);
                await docRef.SetAsync(user);
                _logger.LogInformation("User created successfully: {UserId}", user.Id);
                return user.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user {UserId}", user.Id);
                throw;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            try
            {
                var docRef = _firestoreDb.Collection(USERS_COLLECTION).Document(user.Id);
                await docRef.SetAsync(user, SetOptions.MergeAll);
                _logger.LogInformation("User updated successfully: {UserId}", user.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating user {UserId}", user.Id);
                return false;
            }
        }

        public async Task<bool> DeleteUserAsync(string userId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(USERS_COLLECTION).Document(userId);
                await docRef.DeleteAsync();
                _logger.LogInformation("User deleted successfully: {UserId}", userId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting user {UserId}", userId);
                return false;
            }
        }

        public async Task<List<User>> GetAllUsersAsync()
        {
            try
            {
                var snapshot = await _firestoreDb.Collection(USERS_COLLECTION).GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<User>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all users");
                return new List<User>();
            }
        }

        #endregion

        #region Gmail Account Operations

        public async Task<GmailAccount?> GetAccountAsync(string accountId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(ACCOUNTS_COLLECTION).Document(accountId);
                var snapshot = await docRef.GetSnapshotAsync();
                
                if (snapshot.Exists)
                {
                    return snapshot.ConvertTo<GmailAccount>();
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting account {AccountId}", accountId);
                return null;
            }
        }

        public async Task<string> CreateAccountAsync(GmailAccount account)
        {
            try
            {
                var docRef = _firestoreDb.Collection(ACCOUNTS_COLLECTION).Document(account.Id);
                await docRef.SetAsync(account);
                _logger.LogInformation("Account created successfully: {AccountId}", account.Id);
                return account.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating account {AccountId}", account.Id);
                throw;
            }
        }

        public async Task<bool> UpdateAccountAsync(GmailAccount account)
        {
            try
            {
                var docRef = _firestoreDb.Collection(ACCOUNTS_COLLECTION).Document(account.Id);
                await docRef.SetAsync(account, SetOptions.MergeAll);
                _logger.LogInformation("Account updated successfully: {AccountId}", account.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating account {AccountId}", account.Id);
                return false;
            }
        }

        public async Task<bool> DeleteAccountAsync(string accountId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(ACCOUNTS_COLLECTION).Document(accountId);
                await docRef.DeleteAsync();
                _logger.LogInformation("Account deleted successfully: {AccountId}", accountId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting account {AccountId}", accountId);
                return false;
            }
        }

        public async Task<List<GmailAccount>> GetAllAccountsAsync()
        {
            try
            {
                var snapshot = await _firestoreDb.Collection(ACCOUNTS_COLLECTION).GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<GmailAccount>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all accounts");
                return new List<GmailAccount>();
            }
        }

        public async Task<List<GmailAccount>> GetActiveAccountsAsync()
        {
            try
            {
                var query = _firestoreDb.Collection(ACCOUNTS_COLLECTION)
                    .WhereEqualTo("IsActive", true);
                
                var snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<GmailAccount>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active accounts");
                return new List<GmailAccount>();
            }
        }

        #endregion

        #region Email Filter Operations

        public async Task<EmailFilter?> GetFilterAsync(string filterId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(FILTERS_COLLECTION).Document(filterId);
                var snapshot = await docRef.GetSnapshotAsync();
                
                if (snapshot.Exists)
                {
                    return snapshot.ConvertTo<EmailFilter>();
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filter {FilterId}", filterId);
                return null;
            }
        }

        public async Task<string> CreateFilterAsync(EmailFilter filter)
        {
            try
            {
                var docRef = _firestoreDb.Collection(FILTERS_COLLECTION).Document(filter.Id);
                await docRef.SetAsync(filter);
                _logger.LogInformation("Filter created successfully: {FilterId}", filter.Id);
                return filter.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating filter {FilterId}", filter.Id);
                throw;
            }
        }

        public async Task<bool> UpdateFilterAsync(EmailFilter filter)
        {
            try
            {
                var docRef = _firestoreDb.Collection(FILTERS_COLLECTION).Document(filter.Id);
                await docRef.SetAsync(filter, SetOptions.MergeAll);
                _logger.LogInformation("Filter updated successfully: {FilterId}", filter.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating filter {FilterId}", filter.Id);
                return false;
            }
        }

        public async Task<bool> DeleteFilterAsync(string filterId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(FILTERS_COLLECTION).Document(filterId);
                await docRef.DeleteAsync();
                _logger.LogInformation("Filter deleted successfully: {FilterId}", filterId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting filter {FilterId}", filterId);
                return false;
            }
        }

        public async Task<List<EmailFilter>> GetAllFiltersAsync()
        {
            try
            {
                var snapshot = await _firestoreDb.Collection(FILTERS_COLLECTION).GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<EmailFilter>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all filters");
                return new List<EmailFilter>();
            }
        }

        public async Task<List<EmailFilter>> GetActiveFiltersAsync()
        {
            try
            {
                var query = _firestoreDb.Collection(FILTERS_COLLECTION)
                    .WhereEqualTo("IsActive", true);
                
                var snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<EmailFilter>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active filters");
                return new List<EmailFilter>();
            }
        }

        #endregion

        #region Processed Email Operations

        public async Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(EMAILS_COLLECTION).Document(emailId);
                var snapshot = await docRef.GetSnapshotAsync();

                if (snapshot.Exists)
                {
                    return snapshot.ConvertTo<ProcessedEmail>();
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed email {EmailId}", emailId);
                return null;
            }
        }

        public async Task<string> CreateProcessedEmailAsync(ProcessedEmail email)
        {
            try
            {
                var docRef = _firestoreDb.Collection(EMAILS_COLLECTION).Document(email.Id);
                await docRef.SetAsync(email);
                _logger.LogInformation("Processed email created successfully: {EmailId}", email.Id);
                return email.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating processed email {EmailId}", email.Id);
                throw;
            }
        }

        public async Task<bool> UpdateProcessedEmailAsync(ProcessedEmail email)
        {
            try
            {
                var docRef = _firestoreDb.Collection(EMAILS_COLLECTION).Document(email.Id);
                await docRef.SetAsync(email, SetOptions.MergeAll);
                _logger.LogInformation("Processed email updated successfully: {EmailId}", email.Id);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating processed email {EmailId}", email.Id);
                return false;
            }
        }

        public async Task<bool> DeleteProcessedEmailAsync(string emailId)
        {
            try
            {
                var docRef = _firestoreDb.Collection(EMAILS_COLLECTION).Document(emailId);
                await docRef.DeleteAsync();
                _logger.LogInformation("Processed email deleted successfully: {EmailId}", emailId);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting processed email {EmailId}", emailId);
                return false;
            }
        }

        public async Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync()
        {
            try
            {
                var snapshot = await _firestoreDb.Collection(EMAILS_COLLECTION).GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<ProcessedEmail>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all processed emails");
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail)
        {
            try
            {
                var query = _firestoreDb.Collection(EMAILS_COLLECTION)
                    .WhereEqualTo("AccountEmail", accountEmail);

                var snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<ProcessedEmail>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by account {AccountEmail}", accountEmail);
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                var query = _firestoreDb.Collection(EMAILS_COLLECTION)
                    .WhereGreaterThanOrEqualTo("ProcessedAt", fromDate)
                    .WhereLessThanOrEqualTo("ProcessedAt", toDate);

                var snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<ProcessedEmail>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by date range");
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId)
        {
            try
            {
                var query = _firestoreDb.Collection(EMAILS_COLLECTION)
                    .WhereEqualTo("FilterId", filterId);

                var snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Select(doc => doc.ConvertTo<ProcessedEmail>()).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by filter {FilterId}", filterId);
                return new List<ProcessedEmail>();
            }
        }

        public async Task<bool> EmailExistsAsync(string gmailId)
        {
            try
            {
                var query = _firestoreDb.Collection(EMAILS_COLLECTION)
                    .WhereEqualTo("GmailId", gmailId)
                    .Limit(1);

                var snapshot = await query.GetSnapshotAsync();
                return snapshot.Documents.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if email exists {GmailId}", gmailId);
                return false;
            }
        }

        public async Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds)
        {
            try
            {
                var existingIds = new List<string>();

                // Firestore has a limit of 10 items in WhereIn queries, so we need to batch
                var batches = gmailIds.Chunk(10);

                foreach (var batch in batches)
                {
                    var query = _firestoreDb.Collection(EMAILS_COLLECTION)
                        .WhereIn("GmailId", batch.ToArray());

                    var snapshot = await query.GetSnapshotAsync();
                    var batchExistingIds = snapshot.Documents
                        .Select(doc => doc.GetValue<string>("GmailId"))
                        .ToList();

                    existingIds.AddRange(batchExistingIds);
                }

                return existingIds;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting existing gmail IDs");
                return new List<string>();
            }
        }

        public async Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId)
        {
            try
            {
                var query = _firestoreDb.Collection(EMAILS_COLLECTION)
                    .WhereEqualTo("GmailId", gmailId)
                    .Limit(1);

                var snapshot = await query.GetSnapshotAsync();
                var email = snapshot.Documents.FirstOrDefault()?.ConvertTo<ProcessedEmail>();
                return email;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed email by Gmail ID {GmailId}", gmailId);
                return null;
            }
        }

        public async Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page, int pageSize, string? accountEmail = null, string? filterId = null,
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                Query query = _firestoreDb.Collection(EMAILS_COLLECTION);

                // Apply filters
                if (!string.IsNullOrEmpty(accountEmail))
                {
                    query = query.WhereEqualTo("AccountEmail", accountEmail);
                }

                if (!string.IsNullOrEmpty(filterId))
                {
                    query = query.WhereEqualTo("FilterId", filterId);
                }

                if (fromDate.HasValue)
                {
                    query = query.WhereGreaterThanOrEqualTo("ProcessedAt", fromDate.Value);
                }

                if (toDate.HasValue)
                {
                    query = query.WhereLessThanOrEqualTo("ProcessedAt", toDate.Value);
                }

                // Get total count
                var totalSnapshot = await query.GetSnapshotAsync();
                var totalCount = totalSnapshot.Count;

                // Apply pagination
                query = query.OrderByDescending("ProcessedAt")
                    .Offset(page * pageSize)
                    .Limit(pageSize);

                var snapshot = await query.GetSnapshotAsync();
                var emails = snapshot.Documents.Select(doc => doc.ConvertTo<ProcessedEmail>()).ToList();

                return (emails, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paged processed emails");
                return (new List<ProcessedEmail>(), 0);
            }
        }

        #endregion

        #region Batch Operations

        public async Task<bool> CreateProcessedEmailsBatchAsync(List<ProcessedEmail> emails)
        {
            try
            {
                var batch = _firestoreDb.StartBatch();

                foreach (var email in emails)
                {
                    var docRef = _firestoreDb.Collection(EMAILS_COLLECTION).Document(email.Id);
                    batch.Set(docRef, email);
                }

                await batch.CommitAsync();
                _logger.LogInformation("Batch created {Count} processed emails successfully", emails.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating processed emails batch");
                return false;
            }
        }

        public async Task<bool> UpdateProcessedEmailsBatchAsync(List<ProcessedEmail> emails)
        {
            try
            {
                var batch = _firestoreDb.StartBatch();

                foreach (var email in emails)
                {
                    var docRef = _firestoreDb.Collection(EMAILS_COLLECTION).Document(email.Id);
                    batch.Set(docRef, email, SetOptions.MergeAll);
                }

                await batch.CommitAsync();
                _logger.LogInformation("Batch updated {Count} processed emails successfully", emails.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating processed emails batch");
                return false;
            }
        }

        #endregion

        #region AutoScan Settings Operations

        public async Task<AutoScanSettings?> GetAutoScanSettingsAsync()
        {
            try
            {
                var docRef = _firestoreDb.Collection(SETTINGS_COLLECTION).Document(AUTO_SCAN_SETTINGS_DOC);
                var snapshot = await docRef.GetSnapshotAsync();

                if (snapshot.Exists)
                {
                    return snapshot.ConvertTo<AutoScanSettings>();
                }
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto scan settings");
                return null;
            }
        }

        public async Task<bool> SaveAutoScanSettingsAsync(AutoScanSettings settings)
        {
            try
            {
                var docRef = _firestoreDb.Collection(SETTINGS_COLLECTION).Document(AUTO_SCAN_SETTINGS_DOC);
                await docRef.SetAsync(settings, SetOptions.MergeAll);
                _logger.LogInformation("Auto scan settings saved successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving auto scan settings");
                return false;
            }
        }

        public async Task<bool> DeleteAutoScanSettingsAsync()
        {
            try
            {
                var docRef = _firestoreDb.Collection(SETTINGS_COLLECTION).Document(AUTO_SCAN_SETTINGS_DOC);
                await docRef.DeleteAsync();
                _logger.LogInformation("Auto scan settings deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting auto scan settings");
                return false;
            }
        }

        #endregion
    }
}