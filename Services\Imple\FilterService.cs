﻿using System.Text.Json;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FilterService : IFilterService
    {
        private readonly string _dataFolder;
        private readonly string _filtersFile;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly SemaphoreSlim _fileLock = new(1, 1);
        private readonly ILogger<FilterService>? _logger;

        public FilterService(ILogger<FilterService>? logger = null)
        {
            _logger = logger;
            _dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            _filtersFile = Path.Combine(_dataFolder, "filters.json");

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            Directory.CreateDirectory(_dataFolder);
        }

        public async Task SaveFilterAsync(EmailFilter filter)
        {
            await _fileLock.WaitAsync();
            try
            {
                var filters = await LoadFiltersAsync();
                var existingIndex = filters.FindIndex(f => f.Id == filter.Id);

                if (existingIndex >= 0)
                {
                    // Preserve the original CreatedAt when updating
                    filter.CreatedAt = filters[existingIndex].CreatedAt;
                    filter.UpdatedAt = DateTime.UtcNow;
                    filters[existingIndex] = filter;
                    _logger?.LogInformation($"Filter updated: {filter.Id}");
                }
                else
                {
                    filter.CreatedAt = DateTime.UtcNow;
                    filter.UpdatedAt = DateTime.UtcNow;
                    filters.Add(filter);
                    _logger?.LogInformation($"Filter created: {filter.Id}");
                }

                await SaveFiltersAsync(filters);
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task<EmailFilter?> GetFilterAsync(string filterId)
        {
            var filters = await LoadFiltersAsync();
            return filters.FirstOrDefault(f => f.Id == filterId);
        }

        public async Task<List<EmailFilter>> GetAllFiltersAsync()
        {
            return await LoadFiltersAsync();
        }

        public async Task<List<EmailFilter>> GetActiveFiltersAsync()
        {
            var filters = await LoadFiltersAsync();
            return filters.Where(f => f.IsActive).ToList();
        }

        public async Task UpdateFilterAsync(EmailFilter filter)
        {
            filter.UpdatedAt = DateTime.UtcNow;
            await SaveFilterAsync(filter);
        }

        public async Task DeleteFilterAsync(string filterId)
        {
            await _fileLock.WaitAsync();
            try
            {
                var filters = await LoadFiltersAsync();
                var removedCount = filters.RemoveAll(f => f.Id == filterId);

                if (removedCount > 0)
                {
                    await SaveFiltersAsync(filters);
                    _logger?.LogInformation($"Filter deleted: {filterId}");
                }
            }
            finally
            {
                _fileLock.Release();
            }
        }

        private async Task<List<EmailFilter>> LoadFiltersAsync()
        {
            if (!File.Exists(_filtersFile))
                return new List<EmailFilter>();

            try
            {
                var json = await File.ReadAllTextAsync(_filtersFile);
                return JsonSerializer.Deserialize<List<EmailFilter>>(json, _jsonOptions) ?? new List<EmailFilter>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error loading filters from file");
                return new List<EmailFilter>();
            }
        }

        private async Task SaveFiltersAsync(List<EmailFilter> filters)
        {
            try
            {
                var json = JsonSerializer.Serialize(filters, _jsonOptions);
                await File.WriteAllTextAsync(_filtersFile, json);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error saving filters to file");
                throw;
            }
        }
    }
}