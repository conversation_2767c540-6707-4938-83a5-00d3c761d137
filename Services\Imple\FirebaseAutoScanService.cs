using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FirebaseAutoScanService : IAutoScanService
    {
        private readonly ILogger<FirebaseAutoScanService> _logger;
        private readonly IDataService _dataService;
        private readonly IGmailService _gmailService;
        private readonly IEmailFilterService _filterService;
        private readonly ITemplateService _templateService;
        private readonly IFirebaseService _firebaseService;

        // Use a fixed ID for auto scan settings since there's only one settings object
        private const string AUTO_SCAN_SETTINGS_ID = "auto_scan_settings";

        public FirebaseAutoScanService(
            ILogger<FirebaseAutoScanService> logger,
            IDataService dataService,
            IGmailService gmailService,
            IEmailFilterService filterService,
            ITemplateService templateService,
            IFirebaseService firebaseService)
        {
            _logger = logger;
            _dataService = dataService;
            _gmailService = gmailService;
            _filterService = filterService;
            _templateService = templateService;
            _firebaseService = firebaseService;

            _logger.LogInformation("🔧 FirebaseAutoScanService initialized");
        }

        #region Settings Management

        public async Task<AutoScanSettings?> GetAutoScanSettingsAsync()
        {
            try
            {
                // Try to get settings from Firebase using a fixed document ID
                var settings = await GetAutoScanSettingsFromFirebaseAsync();
                if (settings != null)
                {
                    _logger.LogInformation("📋 Auto-scan settings loaded from Firebase");
                    return settings;
                }

                _logger.LogInformation("📋 Auto-scan settings not found in Firebase, returning default");
                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error loading auto-scan settings from Firebase");
                return null;
            }
        }

        public async Task SaveAutoScanSettingsAsync(AutoScanSettings settings)
        {
            try
            {
                await SaveAutoScanSettingsToFirebaseAsync(settings);
                _logger.LogInformation("💾 Auto-scan settings saved to Firebase successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error saving auto-scan settings to Firebase");
                throw;
            }
        }

        private async Task<AutoScanSettings?> GetAutoScanSettingsFromFirebaseAsync()
        {
            try
            {
                return await _firebaseService.GetAutoScanSettingsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto scan settings from Firebase");
                return null;
            }
        }

        private async Task SaveAutoScanSettingsToFirebaseAsync(AutoScanSettings settings)
        {
            try
            {
                await _firebaseService.SaveAutoScanSettingsAsync(settings);
                _logger.LogInformation("Auto scan settings saved to Firebase successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving auto scan settings to Firebase");
                throw;
            }
        }

        public async Task DeleteAutoScanSettingsAsync()
        {
            try
            {
                await _firebaseService.DeleteAutoScanSettingsAsync();
                _logger.LogInformation("💾 Auto-scan settings deleted from Firebase successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error deleting auto-scan settings from Firebase");
                throw;
            }
        }

        #endregion

        #region Logs Management

        public async Task SaveAutoScanLogAsync(AutoScanLog log)
        {
            try
            {
                // For now, we'll store logs in a separate collection
                // This would need to be implemented in FirebaseService
                _logger.LogInformation("Auto scan log would be saved: {LogId}", log.Id);
                await Task.CompletedTask; // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving auto scan log");
                throw;
            }
        }

        public async Task<List<AutoScanLog>> GetAutoScanLogsAsync(int limit = 20)
        {
            try
            {
                // This would need to be implemented in FirebaseService
                _logger.LogInformation("Getting auto scan logs with limit: {Limit}", limit);
                return new List<AutoScanLog>(); // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto scan logs");
                return new List<AutoScanLog>();
            }
        }

        public async Task<AutoScanLog?> GetAutoScanLogAsync(string logId)
        {
            try
            {
                // This would need to be implemented in FirebaseService
                _logger.LogInformation("Getting auto scan log: {LogId}", logId);
                return null; // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting auto scan log {LogId}", logId);
                return null;
            }
        }

        public async Task DeleteAutoScanLogsAsync(DateTime olderThan)
        {
            try
            {
                // This would need to be implemented in FirebaseService
                _logger.LogInformation("Deleting auto scan logs older than: {Date}", olderThan);
                await Task.CompletedTask; // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting auto scan logs");
                throw;
            }
        }

        #endregion

        #region Auto Scan Operations

        public async Task<bool> StartAutoScanAsync()
        {
            try
            {
                _logger.LogInformation("🚀 Starting auto-scan process...");

                var settings = await GetAutoScanSettingsAsync();
                if (settings == null || !settings.Enabled)
                {
                    _logger.LogWarning("⚠️ Auto-scan is disabled or settings not found");
                    return false;
                }

                var accounts = await _dataService.GetActiveAccountsAsync();
                if (!accounts.Any())
                {
                    _logger.LogWarning("⚠️ No active accounts found for auto-scan");
                    return false;
                }

                var filters = await _dataService.GetActiveFiltersAsync();
                if (!filters.Any())
                {
                    _logger.LogWarning("⚠️ No active filters found for auto-scan");
                    return false;
                }

                var totalProcessed = 0;
                foreach (var account in accounts)
                {
                    try
                    {
                        _logger.LogInformation("📧 Processing account: {Email}", account.Email);
                        var processed = await ProcessAccountEmailsAsync(account, filters, settings);
                        totalProcessed += processed;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ Error processing account {Email}", account.Email);
                    }
                }

                _logger.LogInformation("✅ Auto-scan completed. Total emails processed: {Count}", totalProcessed);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during auto-scan");
                return false;
            }
        }

        public async Task<bool> StopAutoScanAsync()
        {
            try
            {
                _logger.LogInformation("🛑 Stopping auto-scan process...");
                
                var settings = await GetAutoScanSettingsAsync();
                if (settings != null)
                {
                    settings.Enabled = false;
                    await SaveAutoScanSettingsAsync(settings);
                }

                _logger.LogInformation("✅ Auto-scan stopped successfully");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error stopping auto-scan");
                return false;
            }
        }

        public async Task<AutoScanStatus> GetAutoScanStatusAsync()
        {
            try
            {
                var settings = await GetAutoScanSettingsAsync();
                
                return new AutoScanStatus
                {
                    IsEnabled = settings?.Enabled ?? false,
                    LastRunTime = settings?.LastRun,
                    NextRunTime = settings?.NextRun,
                    IntervalMinutes = settings?.Interval ?? 0,
                    IsRunning = false, // This would need to be tracked separately
                    LastRunResult = "Success", // This would need to be tracked
                    TotalEmailsProcessed = 0 // This would need to be tracked
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error getting auto-scan status");
                return new AutoScanStatus
                {
                    IsEnabled = false,
                    IsRunning = false,
                    LastRunResult = "Error: " + ex.Message,
                    IntervalMinutes = 0,
                    TotalEmailsProcessed = 0
                };
            }
        }

        public async Task<AutoScanResult> ExecuteAutoScanAsync(AutoScanSettings settings)
        {
            var result = new AutoScanResult
            {
                StartTime = DateTime.UtcNow,
                Success = false
            };

            try
            {
                _logger.LogInformation("🚀 Executing auto-scan with settings...");

                var accounts = await _dataService.GetActiveAccountsAsync();
                if (!accounts.Any())
                {
                    result.ErrorMessage = "No active accounts found";
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                var filters = await _dataService.GetActiveFiltersAsync();
                if (!filters.Any())
                {
                    result.ErrorMessage = "No active filters found";
                    result.EndTime = DateTime.UtcNow;
                    return result;
                }

                var totalProcessed = 0;
                var accountsProcessed = 0;

                foreach (var account in accounts)
                {
                    try
                    {
                        var processed = await ProcessAccountEmailsAsync(account, filters, settings);
                        totalProcessed += processed;
                        accountsProcessed++;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ Error processing account {Email}", account.Email);
                    }
                }

                result.Success = true;
                result.AccountsProcessed = accountsProcessed;
                result.EmailsProcessed = totalProcessed;
                result.EndTime = DateTime.UtcNow;

                _logger.LogInformation("✅ Auto-scan completed. Accounts: {Accounts}, Emails: {Emails}",
                    accountsProcessed, totalProcessed);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during auto-scan execution");
                result.ErrorMessage = ex.Message;
                result.EndTime = DateTime.UtcNow;
                return result;
            }
        }

        #endregion

        #region Private Helper Methods

        private async Task<int> ProcessAccountEmailsAsync(GmailAccount account, List<EmailFilter> filters, AutoScanSettings settings)
        {
            try
            {
                var processedCount = 0;
                
                // Get recent emails based on settings (default to 7 days if not specified)
                var daysToScan = 7; // Default value since DaysToScan property doesn't exist
                var fromDate = DateTime.UtcNow.AddDays(-daysToScan);
                var emails = await _gmailService.GetEmailsAsync(account.Email, fromDate);

                foreach (var email in emails)
                {
                    try
                    {
                        // Check if email already processed
                        var exists = await _dataService.EmailExistsAsync(email.Id);
                        if (exists) continue;

                        // Apply filters
                        foreach (var filter in filters)
                        {
                            if (_filterService.MatchesFilter(email, filter))
                            {
                                // Extract data using the filter
                                var extractedData = await _filterService.ExtractDataFromEmail(email, filter);

                                // Create processed email
                                var processedEmail = new ProcessedEmail
                                {
                                    Id = Guid.NewGuid().ToString(),
                                    GmailId = email.Id,
                                    AccountEmail = account.Email,
                                    FilterId = filter.Id,
                                    Subject = GetHeaderValue(email.Headers, "Subject"),
                                    FromEmail = GetHeaderValue(email.Headers, "From"),
                                    Body = email.Body,
                                    EmailDate = DateTimeOffset.FromUnixTimeMilliseconds(email.InternalDate).DateTime,
                                    ProcessedAt = DateTime.UtcNow,
                                    ExtractedData = extractedData,
                                    Amount = await _filterService.ExtractAmountFromContentAsync(email.Body),
                                    Currency = await _filterService.ExtractCurrencyFromContentAsync(email.Body)
                                };

                                await _dataService.SaveProcessedEmailAsync(processedEmail);
                                processedCount++;
                                break; // Email processed by one filter, move to next email
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "❌ Error processing email {EmailId}", email.Id);
                    }
                }

                _logger.LogInformation("📊 Account {Email}: {Count} emails processed", account.Email, processedCount);
                return processedCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error processing emails for account {Email}", account.Email);
                return 0;
            }
        }

        private string GetHeaderValue(List<GmailHeader> headers, string name)
        {
            var header = headers.FirstOrDefault(h => h.Name.Equals(name, StringComparison.OrdinalIgnoreCase));
            return header?.Value ?? string.Empty;
        }

        #endregion

        #region Cleanup and Maintenance

        public async Task<int> CleanupOldLogsAsync(int retentionDays = 30)
        {
            try
            {
                _logger.LogInformation("🧹 Starting cleanup of old auto scan logs (keeping {Days} days)...", retentionDays);

                var cutoffDate = DateTime.UtcNow.AddDays(-retentionDays);

                // This would need to be implemented in FirebaseService
                _logger.LogInformation("Would cleanup logs older than: {Date}", cutoffDate);

                return 0; // Placeholder
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during auto scan logs cleanup");
                return 0;
            }
        }

        public async Task<AutoScanStatistics> GetAutoScanStatisticsAsync()
        {
            try
            {
                _logger.LogInformation("📊 Getting auto scan statistics...");

                // This would need to be implemented properly with real data
                var settings = await GetAutoScanSettingsAsync();

                return new AutoScanStatistics
                {
                    TotalRuns = 0,
                    SuccessfulRuns = 0,
                    FailedRuns = 0,
                    TotalEmailsProcessed = 0,
                    TotalAmount = 0,
                    AverageEmailsPerRun = 0,
                    AverageAmountPerRun = 0,
                    LastRunTime = settings?.LastRun,
                    NextRunTime = settings?.NextRun,
                    IsEnabled = settings?.Enabled ?? false,
                    CurrentInterval = settings?.Interval ?? 60,
                    ConfiguredAccounts = settings?.Accounts?.Count ?? 0,
                    ConfiguredFilters = (settings?.Filters?.Count ?? 0) + (settings?.TemplateFilters?.Count ?? 0)
                };
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error getting auto scan statistics");
                return new AutoScanStatistics();
            }
        }

        #endregion

        #region Additional Cleanup Methods

        public async Task<bool> CleanupOldEmailDataAsync(int daysToKeep = 90)
        {
            try
            {
                _logger.LogInformation("🧹 Starting cleanup of old data (keeping {Days} days)...", daysToKeep);

                var cutoffDate = DateTime.UtcNow.AddDays(-daysToKeep);
                var allEmails = await _dataService.GetAllProcessedEmailsAsync();
                var oldEmails = allEmails.Where(e => e.ProcessedAt < cutoffDate).ToList();

                if (!oldEmails.Any())
                {
                    _logger.LogInformation("✅ No old data to cleanup");
                    return true;
                }

                foreach (var email in oldEmails)
                {
                    // Note: IDataService doesn't have DeleteProcessedEmailAsync method
                    // This would need to be implemented or use FirebaseService directly
                    await _firebaseService.DeleteProcessedEmailAsync(email.Id);
                }

                _logger.LogInformation("✅ Cleanup completed. Removed {Count} old emails", oldEmails.Count);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "❌ Error during cleanup");
                return false;
            }
        }

        #endregion
    }
}
