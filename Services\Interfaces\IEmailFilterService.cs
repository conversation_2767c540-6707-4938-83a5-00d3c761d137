﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    /// <summary>
    /// Service for processing emails through various filters and extracting relevant data
    /// </summary>
    public interface IEmailFilterService
    {
        /// <summary>
        /// Process a list of Gmail messages through active filters
        /// </summary>
        /// <param name="messages">Gmail messages to process</param>
        /// <param name="filters">Active email filters to apply</param>
        /// <param name="accountEmail">Email account being processed</param>
        /// <returns>List of successfully processed emails</returns>
        Task<List<ProcessedEmail>> ProcessEmailsAsync(List<GmailMessage> messages, List<EmailFilter> filters, string accountEmail);

        /// <summary>
        /// Extract structured data from a Gmail message using a specific filter
        /// </summary>
        /// <param name="message">Gmail message to extract data from</param>
        /// <param name="filter">Filter containing extraction rules</param>
        /// <returns>Dictionary of extracted key-value pairs</returns>
        Task<Dictionary<string, object>> ExtractDataFromEmail(GmailMessage message, EmailFilter filter);

       

        /// <summary>
        /// Check if a Gmail message matches a specific filter's conditions
        /// </summary>
        /// <param name="message">Gmail message to test</param>
        /// <param name="filter">Filter to test against</param>
        /// <returns>True if message matches filter conditions</returns>
        bool MatchesFilter(GmailMessage message, EmailFilter filter);

        /// <summary>
        /// Extract monetary amount from email content using regex patterns
        /// </summary>
        /// <param name="content">Email content to parse</param>
        /// <returns>Extracted decimal amount or 0 if none found</returns>
        Task<decimal> ExtractAmountFromContentAsync(string content);

        /// <summary>
        /// Extract currency code from email content
        /// </summary>
        /// <param name="content">Email content to parse</param>
        /// <returns>Currency code (default: USD)</returns>
        Task<string> ExtractCurrencyFromContentAsync(string content);
    }
}
