using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FirebaseFilterService : IFilterService
    {
        private readonly IFirebaseService _firebaseService;
        private readonly ILogger<FirebaseFilterService> _logger;

        public FirebaseFilterService(IFirebaseService firebaseService, ILogger<FirebaseFilterService> logger)
        {
            _firebaseService = firebaseService;
            _logger = logger;
        }

        public async Task SaveFilterAsync(EmailFilter filter)
        {
            try
            {
                var existingFilter = await _firebaseService.GetFilterAsync(filter.Id);
                if (existingFilter != null)
                {
                    await _firebaseService.UpdateFilterAsync(filter);
                    _logger.LogInformation("Filter updated: {FilterId} - {Name}", filter.Id, filter.Name);
                }
                else
                {
                    await _firebaseService.CreateFilterAsync(filter);
                    _logger.LogInformation("Filter created: {FilterId} - {Name}", filter.Id, filter.Name);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving filter {FilterId}", filter.Id);
                throw;
            }
        }

        public async Task<EmailFilter?> GetFilterAsync(string filterId)
        {
            try
            {
                return await _firebaseService.GetFilterAsync(filterId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filter {FilterId}", filterId);
                return null;
            }
        }

        public async Task<List<EmailFilter>> GetAllFiltersAsync()
        {
            try
            {
                return await _firebaseService.GetAllFiltersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all filters");
                return new List<EmailFilter>();
            }
        }

        public async Task<List<EmailFilter>> GetActiveFiltersAsync()
        {
            try
            {
                return await _firebaseService.GetActiveFiltersAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active filters");
                return new List<EmailFilter>();
            }
        }

        public async Task UpdateFilterAsync(EmailFilter filter)
        {
            try
            {
                await _firebaseService.UpdateFilterAsync(filter);
                _logger.LogInformation("Filter updated: {FilterId} - {Name}", filter.Id, filter.Name);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating filter {FilterId}", filter.Id);
                throw;
            }
        }

        public async Task DeleteFilterAsync(string filterId)
        {
            try
            {
                await _firebaseService.DeleteFilterAsync(filterId);
                _logger.LogInformation("Filter deleted: {FilterId}", filterId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting filter {FilterId}", filterId);
                throw;
            }
        }

        public async Task<List<EmailFilter>> GetFiltersByTypeAsync(string type)
        {
            try
            {
                var allFilters = await _firebaseService.GetAllFiltersAsync();
                return allFilters.Where(f => f.Type.Equals(type, StringComparison.OrdinalIgnoreCase)).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filters by type {Type}", type);
                return new List<EmailFilter>();
            }
        }

        public async Task<List<EmailFilter>> GetFiltersByTemplateAsync(string templateId)
        {
            try
            {
                var allFilters = await _firebaseService.GetAllFiltersAsync();
                return allFilters.Where(f => f.TemplateId == templateId).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filters by template {TemplateId}", templateId);
                return new List<EmailFilter>();
            }
        }

        public async Task<bool> FilterExistsAsync(string filterId)
        {
            try
            {
                var filter = await _firebaseService.GetFilterAsync(filterId);
                return filter != null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if filter exists {FilterId}", filterId);
                return false;
            }
        }

        public async Task<int> GetFilterCountAsync()
        {
            try
            {
                var filters = await _firebaseService.GetAllFiltersAsync();
                return filters.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting filter count");
                return 0;
            }
        }

        public async Task<int> GetActiveFilterCountAsync()
        {
            try
            {
                var filters = await _firebaseService.GetActiveFiltersAsync();
                return filters.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting active filter count");
                return 0;
            }
        }

        public async Task ToggleFilterStatusAsync(string filterId, bool isActive)
        {
            try
            {
                var filter = await _firebaseService.GetFilterAsync(filterId);
                if (filter != null)
                {
                    filter.IsActive = isActive;
                    await _firebaseService.UpdateFilterAsync(filter);
                    _logger.LogInformation("Filter status toggled: {FilterId} - Active: {IsActive}", filterId, isActive);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error toggling filter status {FilterId}", filterId);
                throw;
            }
        }

        public async Task<List<EmailFilter>> SearchFiltersAsync(string searchTerm)
        {
            try
            {
                var allFilters = await _firebaseService.GetAllFiltersAsync();
                return allFilters.Where(f => 
                    f.Name.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    f.Description.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching filters with term {SearchTerm}", searchTerm);
                return new List<EmailFilter>();
            }
        }

        public async Task BulkUpdateFiltersAsync(List<EmailFilter> filters)
        {
            try
            {
                foreach (var filter in filters)
                {
                    await _firebaseService.UpdateFilterAsync(filter);
                }
                _logger.LogInformation("Bulk updated {Count} filters", filters.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk updating filters");
                throw;
            }
        }

        public async Task<EmailFilter> DuplicateFilterAsync(string filterId, string newName)
        {
            try
            {
                var originalFilter = await _firebaseService.GetFilterAsync(filterId);
                if (originalFilter == null)
                {
                    throw new ArgumentException($"Filter with ID {filterId} not found");
                }

                var duplicatedFilter = new EmailFilter
                {
                    Id = Guid.NewGuid().ToString(),
                    Name = newName,
                    Description = originalFilter.Description + " (Copy)",
                    Type = originalFilter.Type,
                    TemplateId = originalFilter.TemplateId,
                    IsActive = false, // Start as inactive
                    MatchCondition = originalFilter.MatchCondition,
                    Conditions = originalFilter.Conditions.Select(c => new FilterCondition
                    {
                        Id = Guid.NewGuid().ToString(),
                        Type = c.Type,
                        Operator = c.Operator,
                        Value = c.Value
                    }).ToList(),
                    GmailFilters = originalFilter.GmailFilters,
                    ExtractionRules = new Dictionary<string, ExtractionRule>(originalFilter.ExtractionRules),
                    CustomQuery = originalFilter.CustomQuery
                };

                await _firebaseService.CreateFilterAsync(duplicatedFilter);
                _logger.LogInformation("Filter duplicated: {OriginalId} -> {NewId}", filterId, duplicatedFilter.Id);
                
                return duplicatedFilter;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error duplicating filter {FilterId}", filterId);
                throw;
            }
        }
    }
}
