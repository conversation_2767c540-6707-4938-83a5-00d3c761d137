﻿using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IAccountService
    {
        Task SaveAccountAsync(GmailAccount account);
        Task<GmailAccount?> GetAccountAsync(string accountId);
        Task<List<GmailAccount>> GetAllAccountsAsync();
        Task<List<GmailAccount>> GetActiveAccountsAsync();
        Task UpdateAccountAsync(GmailAccount account);
        Task DeleteAccountAsync(string accountId);
    }
}