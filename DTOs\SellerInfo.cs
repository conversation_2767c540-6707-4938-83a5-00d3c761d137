﻿namespace LSB.SellerMailTracker.API.DTOs
{
    public class SellerInfo
    {
        public string Name { get; set; } = string.Empty;
        public string? Location { get; set; }
        public string? Note { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    }

    public class CreateAuthUrlRequest
    {
        public SellerInfo SellerInfo { get; set; } = new();
        public string? RedirectUrl { get; set; }
    }

    public class AuthCallbackRequest
    {
        public string Code { get; set; } = string.Empty;
        public string? State { get; set; }
        public SellerInfo? SellerInfo { get; set; }
    }

    public class AuthStateData
    {
        public SellerInfo? SellerInfo { get; set; }
        public string? RedirectUrl { get; set; }
        public DateTime Timestamp { get; set; }
    }
}
