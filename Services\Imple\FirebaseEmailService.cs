using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FirebaseEmailService : IEmailService
    {
        private readonly IFirebaseService _firebaseService;
        private readonly IAccountService _accountService;
        private readonly ILogger<FirebaseEmailService> _logger;

        public FirebaseEmailService(
            IFirebaseService firebaseService,
            IAccountService accountService,
            ILogger<FirebaseEmailService> logger)
        {
            _firebaseService = firebaseService;
            _accountService = accountService;
            _logger = logger;
        }

        public async Task SaveProcessedEmailAsync(ProcessedEmail email)
        {
            try
            {
                var existingEmail = await _firebaseService.GetProcessedEmailAsync(email.Id);
                if (existingEmail != null)
                {
                    await _firebaseService.UpdateProcessedEmailAsync(email);
                    _logger.LogInformation("Processed email updated: {EmailId}", email.Id);
                }
                else
                {
                    await _firebaseService.CreateProcessedEmailAsync(email);
                    _logger.LogInformation("Processed email created: {EmailId}", email.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving processed email {EmailId}", email.Id);
                throw;
            }
        }

        public async Task SaveProcessedEmailsBatchAsync(List<ProcessedEmail> emails)
        {
            try
            {
                await _firebaseService.CreateProcessedEmailsBatchAsync(emails);
                _logger.LogInformation("Batch saved {Count} processed emails", emails.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error saving processed emails batch");
                throw;
            }
        }

        public async Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId)
        {
            try
            {
                return await _firebaseService.GetProcessedEmailAsync(emailId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed email {EmailId}", emailId);
                return null;
            }
        }

        public async Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync()
        {
            try
            {
                return await _firebaseService.GetAllProcessedEmailsAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting all processed emails");
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail)
        {
            try
            {
                return await _firebaseService.GetProcessedEmailsByAccountAsync(accountEmail);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by account {AccountEmail}", accountEmail);
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            try
            {
                return await _firebaseService.GetProcessedEmailsByDateRangeAsync(fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by date range");
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId)
        {
            try
            {
                return await _firebaseService.GetProcessedEmailsByFilterAsync(filterId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by filter {FilterId}", filterId);
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountIdAsync(string accountId)
        {
            try
            {
                // First get the account to find its email
                var account = await _accountService.GetAccountAsync(accountId);
                if (account == null)
                {
                    _logger.LogWarning("Account not found: {AccountId}", accountId);
                    return new List<ProcessedEmail>();
                }

                // Then get emails by account email
                return await _firebaseService.GetProcessedEmailsByAccountAsync(account.Email);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed emails by account ID {AccountId}", accountId);
                return new List<ProcessedEmail>();
            }
        }

        public async Task<List<ProcessedEmail>> GetRecentProcessedEmailsAsync(int days = 30)
        {
            try
            {
                var fromDate = DateTime.UtcNow.AddDays(-days);
                var toDate = DateTime.UtcNow;
                return await _firebaseService.GetProcessedEmailsByDateRangeAsync(fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting recent processed emails for {Days} days", days);
                return new List<ProcessedEmail>();
            }
        }

        public async Task<bool> EmailExistsAsync(string gmailId)
        {
            try
            {
                return await _firebaseService.EmailExistsAsync(gmailId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if email exists {GmailId}", gmailId);
                return false;
            }
        }

        public async Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId)
        {
            try
            {
                // Use optimized Firebase query instead of getting all emails
                return await _firebaseService.GetProcessedEmailByGmailIdAsync(gmailId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed email by Gmail ID {GmailId}", gmailId);
                return null;
            }
        }

        public async Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds)
        {
            try
            {
                return await _firebaseService.GetExistingGmailIdsAsync(gmailIds);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting existing gmail IDs");
                return new List<string>();
            }
        }

        public async Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page, int pageSize, string? accountEmail = null, string? filterId = null, 
            DateTime? fromDate = null, DateTime? toDate = null)
        {
            try
            {
                return await _firebaseService.GetProcessedEmailsPagedAsync(page, pageSize, accountEmail, filterId, fromDate, toDate);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting paged processed emails");
                return (new List<ProcessedEmail>(), 0);
            }
        }

        public async Task UpdateProcessedEmailAsync(ProcessedEmail email)
        {
            try
            {
                await _firebaseService.UpdateProcessedEmailAsync(email);
                _logger.LogInformation("Processed email updated: {EmailId}", email.Id);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error updating processed email {EmailId}", email.Id);
                throw;
            }
        }

        public async Task DeleteProcessedEmailAsync(string emailId)
        {
            try
            {
                await _firebaseService.DeleteProcessedEmailAsync(emailId);
                _logger.LogInformation("Processed email deleted: {EmailId}", emailId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting processed email {EmailId}", emailId);
                throw;
            }
        }

        public async Task<int> GetProcessedEmailCountAsync()
        {
            try
            {
                var emails = await _firebaseService.GetAllProcessedEmailsAsync();
                return emails.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed email count");
                return 0;
            }
        }

        public async Task<int> GetProcessedEmailCountByAccountAsync(string accountEmail)
        {
            try
            {
                var emails = await _firebaseService.GetProcessedEmailsByAccountAsync(accountEmail);
                return emails.Count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting processed email count by account {AccountEmail}", accountEmail);
                return 0;
            }
        }

        public async Task<decimal> GetTotalAmountAsync()
        {
            try
            {
                var emails = await _firebaseService.GetAllProcessedEmailsAsync();
                return emails.Sum(e => e.Amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total amount");
                return 0;
            }
        }

        public async Task<decimal> GetTotalAmountByAccountAsync(string accountEmail)
        {
            try
            {
                var emails = await _firebaseService.GetProcessedEmailsByAccountAsync(accountEmail);
                return emails.Sum(e => e.Amount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting total amount by account {AccountEmail}", accountEmail);
                return 0;
            }
        }

        public async Task<List<ProcessedEmail>> SearchProcessedEmailsAsync(string searchTerm)
        {
            try
            {
                var allEmails = await _firebaseService.GetAllProcessedEmailsAsync();
                return allEmails.Where(e =>
                    e.Subject.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    e.FromEmail.Contains(searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    e.AccountEmail.Contains(searchTerm, StringComparison.OrdinalIgnoreCase)
                ).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error searching processed emails with term {SearchTerm}", searchTerm);
                return new List<ProcessedEmail>();
            }
        }

        public async Task BulkDeleteProcessedEmailsAsync(List<string> emailIds)
        {
            try
            {
                foreach (var emailId in emailIds)
                {
                    await _firebaseService.DeleteProcessedEmailAsync(emailId);
                }
                _logger.LogInformation("Bulk deleted {Count} processed emails", emailIds.Count);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error bulk deleting processed emails");
                throw;
            }
        }

        public async Task<Dictionary<string, int>> GetEmailCountByCurrencyAsync()
        {
            try
            {
                var emails = await _firebaseService.GetAllProcessedEmailsAsync();
                return emails.GroupBy(e => e.Currency)
                    .ToDictionary(g => g.Key, g => g.Count());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting email count by currency");
                return new Dictionary<string, int>();
            }
        }

        public async Task<Dictionary<string, decimal>> GetAmountByCurrencyAsync()
        {
            try
            {
                var emails = await _firebaseService.GetAllProcessedEmailsAsync();
                return emails.GroupBy(e => e.Currency)
                    .ToDictionary(g => g.Key, g => g.Sum(e => e.Amount));
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting amount by currency");
                return new Dictionary<string, decimal>();
            }
        }
    }
}
