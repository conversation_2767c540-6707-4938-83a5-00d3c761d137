﻿using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services
{
    public class AutoScanBackgroundService : BackgroundService
    {
        private readonly IServiceProvider _serviceProvider;
        private readonly ILogger<AutoScanBackgroundService> _logger;
        private Timer? _cleanupTimer;

        public AutoScanBackgroundService(
            IServiceProvider serviceProvider,
            ILogger<AutoScanBackgroundService> logger)
        {
            _serviceProvider = serviceProvider;
            _logger = logger;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Auto-scan background service started");

            // Start cleanup timer (runs daily at 2 AM)
            StartCleanupTimer();

            while (!stoppingToken.IsCancellationRequested)
            {
                try
                {
                    await CheckAndRunScheduledAutoScan();

                    // Check every minute for scheduled scans
                    await Task.Delay(TimeSpan.FromMinutes(1), stoppingToken);
                }
                catch (OperationCanceledException)
                {
                    // Expected when cancellation is requested
                    break;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error in auto-scan background service");
                    // Wait 5 minutes on error to prevent spam
                    await Task.Delay(TimeSpan.FromMinutes(5), stoppingToken);
                }
            }

            _logger.LogInformation("Auto-scan background service stopped");
        }

        private async Task CheckAndRunScheduledAutoScan()
        {
            using var scope = _serviceProvider.CreateScope();
            var autoScanService = scope.ServiceProvider.GetRequiredService<IAutoScanService>();

            try
            {
                var settings = await autoScanService.GetAutoScanSettingsAsync();

                if (settings == null || !settings.Enabled)
                {
                    // Auto-scan is disabled, nothing to do
                    return;
                }

                // Check if it's time to run
                var now = DateTime.UtcNow;
                if (settings.NextRun.HasValue && settings.NextRun.Value <= now)
                {
                    _logger.LogInformation($"Starting scheduled auto-scan (Next run was: {settings.NextRun:yyyy-MM-dd HH:mm:ss})");

                    try
                    {
                        // Execute auto-scan using the service
                        var result = await autoScanService.ExecuteAutoScanAsync(settings);

                        if (result.Success)
                        {
                            _logger.LogInformation($"Scheduled auto-scan completed successfully. Processed {result.AccountsProcessed} accounts, {result.EmailsProcessed} emails, ${result.TotalAmount:F2}. Next run: {settings.NextRun:yyyy-MM-dd HH:mm:ss}");
                        }
                        else
                        {
                            _logger.LogWarning($"Scheduled auto-scan completed with errors: {result.ErrorMessage}. Next run: {settings.NextRun:yyyy-MM-dd HH:mm:ss}");
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error during scheduled auto-scan execution");

                        // Still update next run time even if execution failed
                        var updatedSettings = await autoScanService.GetAutoScanSettingsAsync();
                        if (updatedSettings != null)
                        {
                            updatedSettings.NextRun = DateTime.UtcNow.AddMinutes(updatedSettings.Interval);
                            await autoScanService.SaveAutoScanSettingsAsync(updatedSettings);
                        }
                    }
                }
                else if (settings.NextRun.HasValue)
                {
                    var timeUntilNext = settings.NextRun.Value - now;
                    if (timeUntilNext.TotalMinutes <= 2) // Log when we're close to the next run
                    {
                        _logger.LogDebug($"Next auto-scan in {timeUntilNext.TotalMinutes:F1} minutes");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking auto-scan schedule");
            }
        }

        private void StartCleanupTimer()
        {
            // Calculate time until next 2 AM
            var now = DateTime.Now;
            var next2AM = now.Date.AddDays(1).AddHours(2); // Tomorrow at 2 AM
            if (now.Hour < 2)
            {
                next2AM = now.Date.AddHours(2); // Today at 2 AM
            }

            var timeUntilCleanup = next2AM - now;

            _logger.LogInformation($"Cleanup timer scheduled for {next2AM:yyyy-MM-dd HH:mm:ss} (in {timeUntilCleanup.TotalHours:F1} hours)");

            _cleanupTimer = new Timer(async _ => await RunDailyCleanup(), null, timeUntilCleanup, TimeSpan.FromDays(1));
        }

        private async Task RunDailyCleanup()
        {
            try
            {
                _logger.LogInformation("Starting daily auto-scan logs cleanup");

                using var scope = _serviceProvider.CreateScope();
                var autoScanService = scope.ServiceProvider.GetRequiredService<IAutoScanService>();

                // Cleanup logs older than 30 days
                var deletedCount = await autoScanService.CleanupOldLogsAsync(30);

                _logger.LogInformation($"Daily cleanup completed: Deleted {deletedCount} old auto-scan logs");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during daily cleanup");
            }
        }

        public override async Task StopAsync(CancellationToken stoppingToken)
        {
            _logger.LogInformation("Auto-scan background service is stopping");

            _cleanupTimer?.Dispose();

            await base.StopAsync(stoppingToken);
        }
    }
}