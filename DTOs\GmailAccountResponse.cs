﻿// Updated GmailAccountResponse.cs
using LSB.SellerMailTracker.API.DTOs;

namespace LSB.SellerMailTracker.API.DTOs
{
    public class GmailAccountResponse
    {
        public string Id { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public DateTime CreatedAt { get; set; }
        public DateTime? LastSyncAt { get; set; }
        public bool IsActive { get; set; }
        public bool TokenExpired { get; set; }

        public SellerInfo? SellerInfo { get; set; }

        public string Status => TokenExpired ? "expired" :
                               IsActive ? "connected" : "pending";
    }
}