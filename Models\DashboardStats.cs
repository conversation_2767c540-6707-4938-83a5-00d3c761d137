﻿namespace LSB.SellerMailTracker.API.Models
{
    public class DashboardStats
    {
        public int TotalAccounts { get; set; }
        public int ActiveFilters { get; set; }
        public int TotalEmailsProcessed { get; set; }
        public decimal TotalAmount { get; set; }
        public DateTime LastProcessedDate { get; set; }
        public Dictionary<string, int> EmailsByType { get; set; } = new();
        public Dictionary<string, decimal> AmountByCurrency { get; set; } = new();
        public Dictionary<string, int> EmailsByPlatform { get; set; } = new();
    }
}