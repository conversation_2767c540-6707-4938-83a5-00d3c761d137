using LSB.SellerMailTracker.API.Models;

namespace LSB.SellerMailTracker.API.Services.Interfaces
{
    public interface IFirebaseService
    {
        // User operations
        Task<User?> GetUserAsync(string userId);
        Task<User?> GetUserByEmailAsync(string email);
        Task<string> CreateUserAsync(User user);
        Task<bool> UpdateUserAsync(User user);
        Task<bool> DeleteUserAsync(string userId);
        Task<List<User>> GetAllUsersAsync();

        // Gmail Account operations
        Task<GmailAccount?> GetAccountAsync(string accountId);
        Task<string> CreateAccountAsync(GmailAccount account);
        Task<bool> UpdateAccountAsync(GmailAccount account);
        Task<bool> DeleteAccountAsync(string accountId);
        Task<List<GmailAccount>> GetAllAccountsAsync();
        Task<List<GmailAccount>> GetActiveAccountsAsync();

        // Email Filter operations
        Task<EmailFilter?> GetFilterAsync(string filterId);
        Task<string> CreateFilterAsync(EmailFilter filter);
        Task<bool> UpdateFilterAsync(EmailFilter filter);
        Task<bool> DeleteFilterAsync(string filterId);
        Task<List<EmailFilter>> GetAllFiltersAsync();
        Task<List<EmailFilter>> GetActiveFiltersAsync();

        // Processed Email operations
        Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId);
        Task<string> CreateProcessedEmailAsync(ProcessedEmail email);
        Task<bool> UpdateProcessedEmailAsync(ProcessedEmail email);
        Task<bool> DeleteProcessedEmailAsync(string emailId);
        Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync();
        Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail);
        Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate);
        Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId);
        Task<bool> EmailExistsAsync(string gmailId);
        Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds);
        Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId);
        Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page, int pageSize, string? accountEmail = null, string? filterId = null, 
            DateTime? fromDate = null, DateTime? toDate = null);

        // Batch operations
        Task<bool> CreateProcessedEmailsBatchAsync(List<ProcessedEmail> emails);
        Task<bool> UpdateProcessedEmailsBatchAsync(List<ProcessedEmail> emails);

        // AutoScan Settings operations
        Task<AutoScanSettings?> GetAutoScanSettingsAsync();
        Task<bool> SaveAutoScanSettingsAsync(AutoScanSettings settings);
        Task<bool> DeleteAutoScanSettingsAsync();
    }
}
