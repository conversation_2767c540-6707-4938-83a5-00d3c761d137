﻿using System.Text.Json;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class EmailService : IEmailService
    {
        private readonly string _dataFolder;
        private readonly string _emailsFile;
        private readonly JsonSerializerOptions _jsonOptions;
        private readonly SemaphoreSlim _fileLock = new(1, 1);
        private readonly ILogger<EmailService>? _logger;
        private readonly IAccountService _accountService;

        public EmailService(IAccountService accountService, ILogger<EmailService>? logger = null)
        {
            _accountService = accountService;
            _logger = logger;
            _dataFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Data");
            _emailsFile = Path.Combine(_dataFolder, "processed_emails.json");

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            };

            Directory.CreateDirectory(_dataFolder);
        }

        public async Task SaveProcessedEmailAsync(ProcessedEmail email)
        {
            await _fileLock.WaitAsync();
            try
            {
                var emails = await LoadProcessedEmailsAsync();
                var existingIndex = emails.FindIndex(e => e.Id == email.Id);

                if (existingIndex >= 0)
                {
                    emails[existingIndex] = email;
                    _logger?.LogInformation($"Email updated: {email.Id}");
                }
                else
                {
                    emails.Add(email);
                    _logger?.LogInformation($"Email saved: {email.Id}");
                }

                await SaveProcessedEmailsAsync(emails);
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task SaveProcessedEmailsBatchAsync(List<ProcessedEmail> emails)
        {
            await _fileLock.WaitAsync();
            try
            {
                var existingEmails = await LoadProcessedEmailsAsync();

                foreach (var email in emails)
                {
                    var existingIndex = existingEmails.FindIndex(e => e.GmailId == email.GmailId);
                    if (existingIndex >= 0)
                        existingEmails[existingIndex] = email;
                    else
                        existingEmails.Add(email);
                }

                await SaveProcessedEmailsAsync(existingEmails);
                _logger?.LogInformation($"Batch saved: {emails.Count} emails");
            }
            finally
            {
                _fileLock.Release();
            }
        }

        public async Task<ProcessedEmail?> GetProcessedEmailAsync(string emailId)
        {
            var emails = await LoadProcessedEmailsAsync();
            return emails.FirstOrDefault(e => e.Id == emailId);
        }

        public async Task<ProcessedEmail?> GetProcessedEmailByGmailIdAsync(string gmailId)
        {
            var emails = await LoadProcessedEmailsAsync();
            return emails.FirstOrDefault(e => e.GmailId == gmailId);
        }

        public async Task<List<ProcessedEmail>> GetAllProcessedEmailsAsync()
        {
            return await LoadProcessedEmailsAsync();
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountAsync(string accountEmail)
        {
            var emails = await LoadProcessedEmailsAsync();
            return emails.Where(e => e.AccountEmail.Equals(accountEmail, StringComparison.OrdinalIgnoreCase)).ToList();
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByAccountIdAsync(string accountId)
        {
            var account = await _accountService.GetAccountAsync(accountId);
            if (account == null) return new List<ProcessedEmail>();

            return await GetProcessedEmailsByAccountAsync(account.Email);
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByDateRangeAsync(DateTime fromDate, DateTime toDate)
        {
            var emails = await LoadProcessedEmailsAsync();
            return emails.Where(e => e.EmailDate >= fromDate && e.EmailDate <= toDate).ToList();
        }

        public async Task<List<ProcessedEmail>> GetProcessedEmailsByFilterAsync(string filterId)
        {
            var emails = await LoadProcessedEmailsAsync();
            return emails.Where(e => e.FilterId == filterId).ToList();
        }

        public async Task<List<ProcessedEmail>> GetRecentProcessedEmailsAsync(int days = 30)
        {
            var emails = await LoadProcessedEmailsAsync();
            var cutoffDate = DateTime.UtcNow.AddDays(-days);
            return emails.Where(e => e.ProcessedAt >= cutoffDate)
                        .OrderByDescending(e => e.ProcessedAt)
                        .ToList();
        }

        public async Task<bool> EmailExistsAsync(string gmailId)
        {
            var emails = await LoadProcessedEmailsAsync();
            return emails.Any(e => e.GmailId == gmailId);
        }

        public async Task<List<string>> GetExistingGmailIdsAsync(List<string> gmailIds)
        {
            var emails = await LoadProcessedEmailsAsync();
            var existingIds = emails.Select(e => e.GmailId).ToHashSet();
            return gmailIds.Where(id => existingIds.Contains(id)).ToList();
        }

        public async Task<(List<ProcessedEmail> emails, int totalCount)> GetProcessedEmailsPagedAsync(
            int page,
            int pageSize,
            string? accountEmail = null,
            string? filterId = null,
            DateTime? fromDate = null,
            DateTime? toDate = null)
        {
            var allEmails = await LoadProcessedEmailsAsync();

            // Apply filters
            if (!string.IsNullOrEmpty(accountEmail))
            {
                allEmails = allEmails.Where(e => e.AccountEmail.Equals(accountEmail, StringComparison.OrdinalIgnoreCase)).ToList();
            }

            if (!string.IsNullOrEmpty(filterId))
            {
                allEmails = allEmails.Where(e => e.FilterId == filterId).ToList();
            }

            if (fromDate.HasValue)
            {
                allEmails = allEmails.Where(e => e.EmailDate >= fromDate.Value).ToList();
            }

            if (toDate.HasValue)
            {
                allEmails = allEmails.Where(e => e.EmailDate <= toDate.Value).ToList();
            }

            // Sort by date descending
            var sortedEmails = allEmails.OrderByDescending(e => e.EmailDate).ToList();
            var totalCount = sortedEmails.Count;

            // Apply pagination
            var paginatedEmails = sortedEmails
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToList();

            return (paginatedEmails, totalCount);
        }

        private async Task<List<ProcessedEmail>> LoadProcessedEmailsAsync()
        {
            if (!File.Exists(_emailsFile))
                return new List<ProcessedEmail>();

            try
            {
                var json = await File.ReadAllTextAsync(_emailsFile);
                return JsonSerializer.Deserialize<List<ProcessedEmail>>(json, _jsonOptions) ?? new List<ProcessedEmail>();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error loading processed emails from file");
                return new List<ProcessedEmail>();
            }
        }

        private async Task SaveProcessedEmailsAsync(List<ProcessedEmail> emails)
        {
            try
            {
                var json = JsonSerializer.Serialize(emails, _jsonOptions);
                await File.WriteAllTextAsync(_emailsFile, json);
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error saving processed emails to file");
                throw;
            }
        }
    }
}