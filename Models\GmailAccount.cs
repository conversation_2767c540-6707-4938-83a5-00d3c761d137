﻿using LSB.SellerMailTracker.API.DTOs;

namespace LSB.SellerMailTracker.API.Models
{
    public class GmailAccount
    {
        public string Id { get; set; } = Guid.NewGuid().ToString();
        public string Email { get; set; } = string.Empty;
        public string DisplayName { get; set; } = string.Empty;
        public string RefreshToken { get; set; } = string.Empty;
        public string AccessToken { get; set; } = string.Empty;
        public DateTime TokenExpiry { get; set; }
        public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
        public DateTime? LastSyncAt { get; set; }
        public bool IsActive { get; set; } = true;

        // ✅ NEW: Status and Seller information
        public string Status { get; set; } = "active"; // active, pending, expired, disabled
        public SellerInfo? SellerInfo { get; set; }

        // Helper properties
        public bool TokenExpired => DateTime.UtcNow >= TokenExpiry;

        // ✅ NEW: Seller display helpers
        public string SellerName => SellerInfo?.Name ?? "Unknown Seller";
        public string SellerLocation => SellerInfo?.Location ?? "";
        public string SellerNote => SellerInfo?.Note ?? "";
    }
}