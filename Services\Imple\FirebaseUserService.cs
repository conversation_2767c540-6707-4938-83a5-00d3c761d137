using LSB.SellerMailTracker.API.DTOs;
using LSB.SellerMailTracker.API.Models;
using LSB.SellerMailTracker.API.Services.Interfaces;
using System.Security.Cryptography;
using System.Text;

namespace LSB.SellerMailTracker.API.Services.Imple
{
    public class FirebaseUserService : IUserService
    {
        private readonly IFirebaseService _firebaseService;
        private readonly IJwtService _jwtService;
        private readonly ILogger<FirebaseUserService> _logger;

        public FirebaseUserService(
            IFirebaseService firebaseService,
            IJwtService jwtService,
            ILogger<FirebaseUserService> logger)
        {
            _firebaseService = firebaseService;
            _jwtService = jwtService;
            _logger = logger;
            
            // Initialize default admin user if not exists
            _ = Task.Run(InitializeDefaultAdminAsync);
        }

        private async Task InitializeDefaultAdminAsync()
        {
            try
            {
                var existingAdmin = await _firebaseService.GetUserByEmailAsync("<EMAIL>");
                if (existingAdmin == null)
                {
                    var adminUser = new User
                    {
                        Id = Guid.NewGuid().ToString(),
                        Email = "<EMAIL>",
                        Name = "Administrator",
                        PasswordHash = HashPassword("admin123"),
                        Role = "Admin",
                        CreatedAt = DateTime.UtcNow,
                        IsActive = true
                    };
                    
                    await _firebaseService.CreateUserAsync(adminUser);
                    _logger.LogInformation("Default admin user created in Firebase");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error initializing default admin user");
            }
        }

        public async Task<ApiResponse<LoginResponse>> LoginAsync(LoginRequest request)
        {
            try
            {
                var user = await GetUserByEmailAsync(request.Email);
                if (user == null || !user.IsActive)
                {
                    return ApiResponse<LoginResponse>.ErrorResult("Email hoặc mật khẩu không đúng");
                }

                if (!await VerifyPasswordAsync(request.Password, user.PasswordHash))
                {
                    return ApiResponse<LoginResponse>.ErrorResult("Email hoặc mật khẩu không đúng");
                }

                // Update last login time
                user.LastLoginAt = DateTime.UtcNow;
                
                // Generate tokens
                var accessToken = _jwtService.GenerateAccessToken(user);
                var refreshToken = _jwtService.GenerateRefreshToken();
                
                // Update refresh token
                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
                
                await UpdateUserAsync(user);

                var response = new LoginResponse
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddHours(1),
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email,
                        Name = user.Name,
                        Role = user.Role,
                        CreatedAt = user.CreatedAt,
                        LastLoginAt = user.LastLoginAt
                    }
                };

                return ApiResponse<LoginResponse>.SuccessResult(response, "Đăng nhập thành công");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during login for email: {Email}", request.Email);
                return ApiResponse<LoginResponse>.ErrorResult("Có lỗi xảy ra trong quá trình đăng nhập");
            }
        }

        public async Task<ApiResponse<LoginResponse>> RegisterAsync(RegisterRequest request)
        {
            try
            {
                // Check if user already exists
                var existingUser = await GetUserByEmailAsync(request.Email);
                if (existingUser != null)
                {
                    return ApiResponse<LoginResponse>.ErrorResult("Email đã được sử dụng");
                }

                // Create new user
                var user = new User
                {
                    Id = Guid.NewGuid().ToString(),
                    Email = request.Email,
                    Name = request.Name,
                    PasswordHash = HashPassword(request.Password),
                    Role = "User",
                    CreatedAt = DateTime.UtcNow,
                    LastLoginAt = DateTime.UtcNow,
                    IsActive = true
                };

                // Generate tokens
                var accessToken = _jwtService.GenerateAccessToken(user);
                var refreshToken = _jwtService.GenerateRefreshToken();
                
                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);

                await CreateUserAsync(user);

                var response = new LoginResponse
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddHours(1),
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email,
                        Name = user.Name,
                        Role = user.Role,
                        CreatedAt = user.CreatedAt,
                        LastLoginAt = user.LastLoginAt
                    }
                };

                return ApiResponse<LoginResponse>.SuccessResult(response, "Đăng ký thành công");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during registration for email: {Email}", request.Email);
                return ApiResponse<LoginResponse>.ErrorResult("Có lỗi xảy ra trong quá trình đăng ký");
            }
        }

        public async Task<ApiResponse<LoginResponse>> RefreshTokenAsync(RefreshTokenRequest request)
        {
            try
            {
                var users = await _firebaseService.GetAllUsersAsync();
                var user = users.FirstOrDefault(u => u.RefreshToken == request.RefreshToken);
                
                if (user == null || user.RefreshTokenExpiryTime <= DateTime.UtcNow)
                {
                    return ApiResponse<LoginResponse>.ErrorResult("Refresh token không hợp lệ hoặc đã hết hạn");
                }

                // Generate new tokens
                var accessToken = _jwtService.GenerateAccessToken(user);
                var refreshToken = _jwtService.GenerateRefreshToken();
                
                user.RefreshToken = refreshToken;
                user.RefreshTokenExpiryTime = DateTime.UtcNow.AddDays(7);
                
                await UpdateUserAsync(user);

                var response = new LoginResponse
                {
                    Token = accessToken,
                    RefreshToken = refreshToken,
                    ExpiresAt = DateTime.UtcNow.AddHours(1),
                    User = new UserInfo
                    {
                        Id = user.Id,
                        Email = user.Email,
                        Name = user.Name,
                        Role = user.Role,
                        CreatedAt = user.CreatedAt,
                        LastLoginAt = user.LastLoginAt
                    }
                };

                return ApiResponse<LoginResponse>.SuccessResult(response, "Token đã được làm mới");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during token refresh");
                return ApiResponse<LoginResponse>.ErrorResult("Có lỗi xảy ra trong quá trình làm mới token");
            }
        }

        public async Task<ApiResponse<object>> LogoutAsync(string userId)
        {
            try
            {
                var user = await GetUserByIdAsync(userId);
                if (user != null)
                {
                    user.RefreshToken = null;
                    user.RefreshTokenExpiryTime = null;
                    await UpdateUserAsync(user);
                }

                return ApiResponse<object>.SuccessResult(null, "Đăng xuất thành công");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during logout for user: {UserId}", userId);
                return ApiResponse<object>.ErrorResult("Có lỗi xảy ra trong quá trình đăng xuất");
            }
        }

        public async Task<User?> GetUserByEmailAsync(string email)
        {
            return await _firebaseService.GetUserByEmailAsync(email);
        }

        public async Task<User?> GetUserByIdAsync(string id)
        {
            return await _firebaseService.GetUserAsync(id);
        }

        public async Task<bool> CreateUserAsync(User user)
        {
            try
            {
                await _firebaseService.CreateUserAsync(user);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error creating user {UserId}", user.Id);
                return false;
            }
        }

        public async Task<bool> UpdateUserAsync(User user)
        {
            return await _firebaseService.UpdateUserAsync(user);
        }

        public async Task<bool> VerifyPasswordAsync(string password, string hash)
        {
            await Task.Delay(1); // Simulate async operation
            return HashPassword(password) == hash;
        }

        public string HashPassword(string password)
        {
            using var sha256 = SHA256.Create();
            var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "salt"));
            return Convert.ToBase64String(hashedBytes);
        }
    }
}
